"""
Java代码结构问题检查器
基于业务规则对Java代码结构进行检查，如方法返回类型、注解内容、特定属性等
"""
import json
import re
from typing import List, Dict, Any
from datetime import datetime


class StructureChecker:
    """Java代码结构问题检查器"""

    def __init__(self, rules_file: str = "rules/structure_rules.json", project_root: str = None):
        """
        初始化结构检查器

        Args:
            rules_file: 结构规则配置文件路径
            project_root: 项目根目录路径（用于常量查找）
        """
        self.rules_file = rules_file
        self.project_root = project_root
        self.issues = []
        self.rules = self._load_rules()

    def _load_rules(self) -> Dict[str, Any]:
        """加载结构检查规则"""
        try:
            with open(self.rules_file, 'r', encoding='utf-8') as f:
                rules = json.load(f)
                if not rules:
                    raise ValueError(f"规则文件 {self.rules_file} 为空")
                return rules
        except (FileNotFoundError, json.JSONDecodeError, ValueError) as e:
            raise Exception(f"无法加载结构规则文件 {self.rules_file}: {e}")

    def check_file_structures(self, file_structures: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """
        动态问题检查
        Args:
            file_structures: 文件结构信息列表
        Returns:
            问题列表
        """
        print("开始结构问题检查...")
        self.issues = []

        for file_structure in file_structures:
            if file_structure.get("parse_error"):
                continue

            file_path = file_structure.get("file_path", "")
            print(f"动态问题检查: {file_path}")

            # 检查类结构问题
            for class_info in file_structure.get("classes", []):
                self.issues.extend(self._check_class_structure(class_info, file_path))

            # 检查接口结构问题
            for interface_info in file_structure.get("interfaces", []):
                self.issues.extend(self._check_interface_structure(interface_info, file_path))

        print(f"结构检查完成，发现 {len(self.issues)} 个问题")
        return self.issues

    def _check_class_structure(self, class_info: Dict[str, Any], file_path: str) -> List[Dict[str, Any]]:
        """检查类结构问题"""
        issues = []
        class_name = class_info.get("name", "")

        # 1. 检查类注解（@RequestMapping规则等）
        issues.extend(self._check_class_annotations(class_info, file_path, class_name))

        # 2. 检查方法返回类型规则
        for method in class_info.get("methods", []):
            issues.extend(self._check_method_return_type(method, file_path, class_name))
            issues.extend(self._check_method_annotations(method, file_path, class_name))

        # 3. 检查目标检测规则
        issues.extend(self._check_target_detection(class_info, file_path, class_name))

        # 4. 检查正则表达式模式匹配规则
        issues.extend(self._check_regex_patterns(class_info, file_path, class_name))

        return issues

    def _check_interface_structure(self, interface_info: Dict[str, Any], file_path: str) -> List[Dict[str, Any]]:
        """检查接口结构问题"""
        issues = []
        interface_name = interface_info.get("name", "")

        # 检查接口方法返回类型
        for method in interface_info.get("methods", []):
            issues.extend(self._check_method_return_type(method, file_path, interface_name))
            issues.extend(self._check_method_annotations(method, file_path, interface_name))

        return issues

    def _check_method_return_type(self, method_info: Dict[str, Any], file_path: str, class_name: str) -> List[
        Dict[str, Any]]:
        """
        检查方法返回类型规则
        场景1: 针对所有方法的返回类型，支持多个规则校验
        支持泛型匹配，如 Result<T> 可以匹配 Result<List<VoltageSideMonitorVo>>
        """
        issues = []
        return_type_rules_list = self.rules.get("method_return_type_rules", [])
        method_name = method_info.get("name", "")
        return_type = method_info.get("return_type", "")

        # 遍历每个返回类型规则
        for rule in return_type_rules_list:
            if not rule.get("enabled", True):
                continue

            forbidden_types = rule.get("forbidden_return_types", [])
            severity = rule.get("severity", "warning")
            message_template = rule.get("message_template", "方法 '{method_name}' 的返回类型 '{return_type}' 不被允许")
            problemtype = rule.get("problemtype", "METHOD_RETURN_TYPE_CHECK")

            # 检查是否使用了禁止的返回类型（支持泛型匹配）
            matched_forbidden_type = self._match_return_type(return_type, forbidden_types)
            if matched_forbidden_type:
                issues.append({
                    "file": file_path,
                    "error_type": problemtype,  # 使用 problemtype 作为 error_type
                    "category": "method_return_type",
                    "severity": severity,
                    "message": message_template.format(method_name=method_name, return_type=return_type),
                    "line": method_info.get("start_line", 0),
                    "element": "method",
                    "element_name": method_name,
                    "class_name": class_name,
                    "return_type": return_type,
                    "forbidden_types": forbidden_types,
                    "matched_forbidden_type": matched_forbidden_type,
                    "rule_type": "forbidden_return_type",
                    "problemtype": problemtype
                })

        return issues

    def _match_return_type(self, actual_return_type: str, forbidden_types: List[str]) -> str:
        """
        匹配返回类型，支持泛型匹配
        优先返回最具体的匹配（泛型匹配优先于基础类型匹配）

        Args:
            actual_return_type: 实际的返回类型，如 "Result<List<VoltageSideMonitorVo>>"
            forbidden_types: 禁止的返回类型列表，如 ["Result", "Result<T>", "ResultWithTotal"]

        Returns:
            匹配到的禁止类型，如果没有匹配则返回空字符串
        """
        if not actual_return_type or not forbidden_types:
            return ""

        # 收集所有匹配的禁止类型
        matches = []
        for forbidden_type in forbidden_types:
            if self._is_type_match(actual_return_type, forbidden_type):
                matches.append(forbidden_type)

        if not matches:
            return ""

        # 优先返回更具体的匹配
        # 1. 精确匹配优先级最高
        for match in matches:
            if match == actual_return_type:
                return match

        # 2. 泛型匹配优先于基础类型匹配
        generic_matches = [m for m in matches if '<' in m]
        if generic_matches:
            # 返回第一个泛型匹配
            return generic_matches[0]

        # 3. 返回第一个基础类型匹配
        return matches[0]

    def _is_type_match(self, actual_type: str, forbidden_type: str) -> bool:
        """
        判断实际类型是否匹配禁止类型

        支持的匹配模式：
        1. 精确匹配：Result == Result
        2. 基础类型匹配：Result 匹配 Result<任何泛型>
        3. 泛型通配符匹配：Result<T> 匹配 Result<任何泛型>

        Args:
            actual_type: 实际类型，如 "Result<List<VoltageSideMonitorVo>>"
            forbidden_type: 禁止类型模式，如 "Result" 或 "Result<T>"

        Returns:
            是否匹配
        """
        # 1. 精确匹配
        if actual_type == forbidden_type:
            return True

        # 2. 泛型通配符匹配：Result<T>、Result<*>、Result<?> 匹配任何 Result<...>
        if '<' in forbidden_type:
            forbidden_base = forbidden_type.split('<')[0]
            forbidden_generic_part = forbidden_type[len(forbidden_base):]

            # 支持的泛型通配符（T、*、? 都表示任意类型）
            if forbidden_generic_part in ['<T>', '<*>', '<?>'] and actual_type.startswith(forbidden_base + '<'):
                return True

        # 3. 基础类型匹配：Result 匹配 Result<任何泛型>
        elif '<' in actual_type:
            actual_base = actual_type.split('<')[0]
            if actual_base == forbidden_type:
                return True

        return False

    def _check_class_annotations(self, class_info: Dict[str, Any], file_path: str, class_name: str) -> List[
        Dict[str, Any]]:
        """
        检查类注解规则
        支持多种注解检查，包括@RequestMapping和@OperationLog等
        """
        issues = []
        annotation_rules = self.rules.get("annotation_issues_rules", [])
        
        # 兼容旧的对象格式
        if isinstance(annotation_rules, dict):
            annotation_rules = [annotation_rules]

        annotations = class_info.get("annotations", [])

        for annotation in annotations:
            annotation_name = annotation.get("name", "").replace("@", "")
            annotation_line = annotation.get("line", class_info.get("start_line", 0))
            
            # 遍历所有注解规则
            for rule in annotation_rules:
                if not rule.get("enabled", True):
                    continue
                    
                rule_annotation_name = rule.get("annotation_name", "").replace("@", "")
                check_class = rule.get("check_class_annotations", True)
                
                if annotation_name == rule_annotation_name and check_class:
                    issues.extend(self._check_annotation_content(
                        annotation, file_path, class_name, "class",
                        annotation_line, rule
                    ))

        return issues

    def _check_method_annotations(self, method_info: Dict[str, Any], file_path: str, class_name: str) -> List[
        Dict[str, Any]]:
        """
        检查方法注解规则
        支持多种注解检查，包括@RequestMapping和@OperationLog等
        """
        issues = []
        annotation_rules = self.rules.get("annotation_issues_rules", [])
        
        # 兼容旧的对象格式
        if isinstance(annotation_rules, dict):
            annotation_rules = [annotation_rules]

        method_name = method_info.get("name", "")
        annotations = method_info.get("annotations", [])

        for annotation in annotations:
            annotation_name = annotation.get("name", "").replace("@", "")
            annotation_line = annotation.get("line", method_info.get("start_line", 0))
            
            # 遍历所有注解规则
            for rule in annotation_rules:
                if not rule.get("enabled", True):
                    continue
                    
                rule_annotation_name = rule.get("annotation_name", "").replace("@", "")
                check_method = rule.get("check_method_annotations", True)
                
                if annotation_name == rule_annotation_name and check_method:
                    issues.extend(self._check_annotation_content(
                        annotation, file_path, class_name, "method",
                        annotation_line, rule, method_name
                    ))

        return issues

    def _check_annotation_content(self, annotation: Dict[str, Any], file_path: str,
                                 class_name: str, element_type: str, line_number: int,
                                 rule: Dict[str, Any], method_name: str = None) -> List[Dict[str, Any]]:
        """
        通用注解内容检查方法，支持多种注解类型
        """
        issues = []
        annotation_name = rule.get("annotation_name", "")
        
        if annotation_name == "RequestMapping":
            return self._check_request_mapping_rule(annotation, file_path, class_name, 
                                                   element_type, line_number, rule, method_name)
        elif annotation_name == "OperationLog":
            return self._check_operation_log_rule(annotation, file_path, class_name,
                                                 element_type, line_number, rule, method_name)
        
        return issues

    def _check_request_mapping_rule(self, annotation: Dict[str, Any], file_path: str,
                                   class_name: str, element_type: str, line_number: int,
                                   rule: Dict[str, Any], method_name: str = None) -> List[Dict[str, Any]]:
        """
        检查@RequestMapping注解的value内容
        """
        issues = []
        required_keywords = rule.get("required_keywords", ["PluginInfoDef"])

        # 获取注解的value参数
        annotation_arguments = annotation.get("arguments", [])
        value_content = ""

        for arg in annotation_arguments:
            arg_name = arg.get("name", "")
            arg_value = arg.get("value", "")

            # 查找value参数或默认参数
            if arg_name == "value" or arg_name == "":
                value_content = str(arg_value)
                break

        # 检查是否包含必需的关键字
        missing_keywords = []
        for keyword in required_keywords:
            if keyword not in value_content:
                missing_keywords.append(keyword)

        if missing_keywords:
            severity = rule.get("severity", "error")
            problemtype = rule.get("problemtype", "ANNOTATION_CONTENT_CHECK")
            suggest = rule.get("suggest", "请检查注解内容是否符合规范")
            element_name = method_name if method_name else class_name
            element_desc = f"方法 '{method_name}'" if method_name else f"类 '{class_name}'"

            issues.append({
                "file": file_path,
                "type": "structure_check",
                "category": "annotation_content",
                "severity": severity,
                "message": f"{element_desc} 的 @RequestMapping 注解缺少必需关键字: {', '.join(missing_keywords)}",
                "line": line_number,
                "element": element_type,
                "element_name": element_name,
                "class_name": class_name,
                "annotation_name": "@RequestMapping",
                "annotation_value": value_content,
                "missing_keywords": missing_keywords,
                "rule_type": "annotation_content_check",
                "problemtype": problemtype,
                "suggest": suggest
            })

        return issues

    def _check_operation_log_rule(self, annotation: Dict[str, Any], file_path: str,
                                 class_name: str, element_type: str, line_number: int,
                                 rule: Dict[str, Any], method_name: str = None) -> List[Dict[str, Any]]:
        """
        检查@OperationLog注解的operationType属性值范围
        """
        issues = []
        target_attribute = rule.get("target_attribute", "operationType")
        value_range = rule.get("value_range", {"min": 10000, "max": 20000})
        constant_matching = rule.get("constant_matching", True)

        # 获取注解的目标属性参数
        annotation_arguments = annotation.get("arguments", [])
        attribute_value = None
        raw_value = None

        for arg in annotation_arguments:
            arg_name = arg.get("name", "")
            arg_value = arg.get("value", "")

            if arg_name == target_attribute:
                raw_value = str(arg_value)
                attribute_value = arg_value
                break

        if attribute_value is None:
            return issues

        # 尝试解析常量值
        actual_value = None
        if constant_matching:
            actual_value = self._resolve_constant_value(raw_value)

        # 如果不是常量或无法解析，尝试直接解析数值
        if actual_value is None:
            try:
                actual_value = int(raw_value)
            except (ValueError, TypeError):
                # 无法解析为数值，跳过检查
                return issues

        # 检查数值范围
        min_value = value_range.get("min", 10000)
        max_value = value_range.get("max", 20000)

        if not (min_value <= actual_value <= max_value):
            severity = rule.get("severity", "warning")
            problemtype = rule.get("problemtype", "ANNOTATION_CONTENT_CHECK")
            suggest = rule.get("suggest", "请检查注解属性值是否在允许的范围内")

            element_name = method_name if method_name else class_name
            element_desc = f"方法 '{method_name}'" if method_name else f"类 '{class_name}'"

            issues.append({
                "file": file_path,
                "type": "structure_check",
                "category": "annotation_content",
                "severity": severity,
                "message": f"{element_desc} 的 @OperationLog 注解 {target_attribute} 值超出范围: {actual_value} (应在 [{min_value}, {max_value}] 内)",
                "line": line_number,
                "element": element_type,
                "element_name": element_name,
                "class_name": class_name,
                "annotation_name": "@OperationLog",
                "annotation_attribute": target_attribute,
                "raw_value": raw_value,
                "actual_value": actual_value,
                "expected_range": f"[{min_value}, {max_value}]",
                "rule_type": "annotation_content_check",
                "problemtype": problemtype,
                "suggest": suggest
            })

        return issues

    def _resolve_constant_value(self, constant_reference: str) -> int:
        """
        解析常量引用的实际数值
        Args:
            constant_reference: 常量引用，如 "GroupEnergyConstantDef.CLASSES_SCHEME" 或 "CLASSES_SCHEME"
        Returns:
            常量的实际数值，如果无法解析则返回None
        """
        if not constant_reference:
            return None

        # 清理常量引用
        constant_ref = constant_reference.strip()

        # 如果已经是数字，直接返回
        try:
            return int(constant_ref)
        except (ValueError, TypeError):
            pass

        # 尝试解析常量引用
        if "." in constant_ref:
            # 形如 ClassName.CONSTANT_NAME 的引用
            parts = constant_ref.split(".")
            class_name = parts[0]
            constant_name = parts[-1]
        else:
            # 直接的常量名
            constant_name = constant_ref
            class_name = None

        # 直接在项目中查找常量定义（不依赖当前文件）
        return self._find_constant_in_project(constant_name, class_name)

    def _get_project_root(self) -> str:
        """
        获取项目根路径
        """
        import os

        # 如果构造函数中指定了项目根路径，直接使用
        if self.project_root:
            return os.path.abspath(self.project_root)

        # 否则从当前文件向上查找项目根目录
        current_dir = os.path.dirname(os.path.abspath(__file__))

        # 向上查找，直到找到包含常见项目标识的目录
        project_indicators = [
            'pom.xml',      # Maven项目
            'build.gradle', # Gradle项目
            '.git',         # Git仓库
            'src',          # 源码目录
            'main.py',      # 当前扫描工具的主文件
        ]

        search_dir = current_dir
        for _ in range(5):  # 最多向上查找5级目录
            for indicator in project_indicators:
                if os.path.exists(os.path.join(search_dir, indicator)):
                    return search_dir

            parent_dir = os.path.dirname(search_dir)
            if parent_dir == search_dir:  # 已经到达根目录
                break
            search_dir = parent_dir

        # 如果没有找到项目标识，返回当前目录的上级目录
        return os.path.dirname(current_dir)

    def _find_constant_in_file(self, file_path: str, constant_name: str, class_name: str = None) -> int:
        """
        在指定文件中查找常量定义
        """
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()

            # 使用正则表达式查找常量定义
            import re

            # 如果指定了类名，先检查文件是否包含该类
            if class_name:
                class_pattern = rf'class\s+{re.escape(class_name)}\s*\{{'
                if not re.search(class_pattern, content, re.IGNORECASE):
                    return None

            # 查找各种形式的常量定义
            patterns = [
                # public static final int CONSTANT_NAME = value;
                rf'public\s+static\s+final\s+int\s+{re.escape(constant_name)}\s*=\s*(\d+)',
                # static final int CONSTANT_NAME = value;
                rf'static\s+final\s+int\s+{re.escape(constant_name)}\s*=\s*(\d+)',
                # final int CONSTANT_NAME = value;
                rf'final\s+int\s+{re.escape(constant_name)}\s*=\s*(\d+)',
                # int CONSTANT_NAME = value;
                rf'int\s+{re.escape(constant_name)}\s*=\s*(\d+)',
                # String类型的常量（可能包含数字）
                rf'public\s+static\s+final\s+String\s+{re.escape(constant_name)}\s*=\s*"(\d+)"',
                rf'static\s+final\s+String\s+{re.escape(constant_name)}\s*=\s*"(\d+)"',
                # 其他数字类型
                rf'public\s+static\s+final\s+(?:long|Long)\s+{re.escape(constant_name)}\s*=\s*(\d+)',
                rf'static\s+final\s+(?:long|Long)\s+{re.escape(constant_name)}\s*=\s*(\d+)',
            ]

            for pattern in patterns:
                matches = re.findall(pattern, content, re.IGNORECASE | re.MULTILINE)
                if matches:
                    try:
                        return int(matches[0])
                    except (ValueError, TypeError):
                        continue

        except Exception:
            pass

        return None

    def _find_constant_in_project(self, constant_name: str, class_name: str = None) -> int:
        """
        在整个项目中查找常量定义
        """
        import os
        import glob

        # 获取项目根目录
        project_root = self._get_project_root()

        # 如果有指定类名，优先查找该类
        if class_name:
            # 查找可能的Java文件
            possible_files = []

            # 在项目中搜索包含该类名的Java文件
            for root, dirs, files in os.walk(project_root):
                for file in files:
                    if file.endswith('.java') and (class_name in file or file == f"{class_name}.java"):
                        possible_files.append(os.path.join(root, file))

            # 在找到的文件中查找常量
            for file_path in possible_files:
                try:
                    constant_value = self._find_constant_in_file(file_path, constant_name, class_name)
                    if constant_value is not None:
                        return constant_value
                except Exception:
                    continue

        # 如果没有找到，在所有Java文件中搜索
        try:
            java_files = glob.glob(os.path.join(project_root, "**", "*.java"), recursive=True)
            for file_path in java_files[:50]:  # 限制搜索文件数量，避免性能问题
                try:
                    constant_value = self._find_constant_in_file(file_path, constant_name, class_name)
                    if constant_value is not None:
                        return constant_value
                except Exception:
                    continue
        except Exception:
            pass

        return None

    def _check_target_detection(self, class_info: Dict[str, Any], file_path: str, class_name: str) -> List[
        Dict[str, Any]]:
        """
        检查目标检测规则
        场景3: 检查类的使用（字段声明和方法调用），而不是类定义
        """
        issues = []
        target_rules = self.rules.get("target_detection_rules", {})

        if not target_rules.get("enabled", False):
            return issues

        detection_list = target_rules.get("detection_list", [])
        severity = target_rules.get("severity", "info")

        for detection_rule in detection_list:
            target_class_name = detection_rule.get("class_name", "")
            target_methods = detection_rule.get("methods", [])
            suggest = detection_rule.get("suggest", "")
            problemtype = detection_rule.get("problemtype", "")

            # 收集该目标类的所有使用信息
            target_usage = self._collect_target_class_usage(
                class_info, file_path, class_name, target_class_name, target_methods
            )

            # 如果有使用，生成一个合并的问题报告
            if target_usage:
                issues.append({
                    "file": file_path,
                    "error_type": problemtype,         # 交换：使用 problemtype 作为 error_type
                    "error_code": "target_detection",  # 交换：使用 target_detection 作为 error_code
                    "category": "target_detection",
                    "severity": severity,
                    "message": f"类 '{class_name}' 中使用了废弃的 '{target_class_name}'",
                    "line": target_usage["line_info"],
                    "element": "class_usage",
                    "element_name": f"{class_name} -> {target_class_name}",
                    "class_name": class_name,
                    "target_class": target_class_name,
                    "usage_details": target_usage["details"],
                    "suggest": suggest,
                    "problemtype": problemtype,
                    "rule_type": "target_detection_usage"
                })

        return issues

    def _is_target_class_used(self, field_type: str, target_class_name: str) -> bool:
        """
        检查字段类型是否使用了目标类
        支持泛型类型，如 List<TargetClass>
        """
        if not field_type or not target_class_name:
            return False

        # 直接匹配
        if target_class_name in field_type:
            return True

        # 简单类名匹配（去掉包名）
        simple_target_name = target_class_name.split('.')[-1]
        if simple_target_name in field_type:
            return True

        return False

    def _collect_target_class_usage(self, class_info: Dict[str, Any], file_path: str,
                                   class_name: str, target_class_name: str, target_methods: List[str]) -> Dict[str, Any]:
        """
        收集目标类在当前类中的所有使用信息
        """
        usage_info = {
            "line_info": {},
            "details": {
                "fields": [],
                "method_calls": []
            }
        }

        all_lines = []

        # 1. 收集字段声明和使用
        for field in class_info.get("fields", []):
            field_type = field.get("type", "")
            field_name = field.get("name", "")

            if self._is_target_class_used(field_type, target_class_name):
                declaration_line = field.get("start_line", 0)
                usage_lines = self._find_field_usage_lines(file_path, field_name, class_info)

                usage_info["details"]["fields"].append({
                    "field_name": field_name,
                    "field_type": field_type,
                    "declaration_line": declaration_line,
                    "usage_lines": usage_lines
                })

                # 收集所有相关行号
                all_lines.append(declaration_line)
                all_lines.extend(usage_lines)

        # 2. 收集方法调用
        method_calls = self._find_method_calls_in_class(
            class_info, file_path, target_class_name, target_methods
        )

        for call in method_calls:
            usage_info["details"]["method_calls"].append(call)
            all_lines.append(call["line"])

        # 3. 构建 line_info
        if usage_info["details"]["fields"]:
            # 如果有字段，使用字段的声明和使用格式
            field_info = usage_info["details"]["fields"][0]  # 取第一个字段
            usage_info["line_info"]["声明"] = field_info["declaration_line"]

            # 合并字段使用行和方法调用行
            all_usage_lines = field_info["usage_lines"].copy()
            all_usage_lines.extend([call["line"] for call in method_calls])
            all_usage_lines = sorted(list(set(all_usage_lines)))  # 去重并排序

            if all_usage_lines:
                usage_info["line_info"]["使用"] = all_usage_lines
        else:
            # 如果只有方法调用，直接使用调用行
            call_lines = [call["line"] for call in method_calls]
            if call_lines:
                usage_info["line_info"]["使用"] = sorted(call_lines)

        # 如果没有任何使用，返回空
        if not usage_info["details"]["fields"] and not usage_info["details"]["method_calls"]:
            return None

        return usage_info

    def _find_field_usage_lines(self, file_path: str, field_name: str, class_info: Dict[str, Any]) -> List[int]:
        """
        查找字段在类中的使用行
        """
        usage_lines = []

        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                lines = f.readlines()

            # 获取类的范围
            class_start = class_info.get("start_line", 1) - 1  # 转换为0基索引
            class_end = class_info.get("end_line", len(lines))

            # 在类范围内查找字段使用
            for line_num in range(class_start, min(class_end, len(lines))):
                line_content = lines[line_num].strip()

                # 跳过注释行和字段声明行
                if (line_content.startswith("//") or
                    line_content.startswith("/*") or
                    line_content.startswith("*") or
                    f"{field_name};" in line_content or  # 字段声明行
                    f"private {field_name}" in line_content or
                    f"public {field_name}" in line_content or
                    f"protected {field_name}" in line_content):
                    continue

                # 查找字段使用模式
                if self._is_field_used_in_line(line_content, field_name):
                    usage_lines.append(line_num + 1)  # 转换为1基索引

        except Exception as e:
            print(f"查找字段使用行时出错: {e}")

        return usage_lines

    def _is_field_used_in_line(self, line_content: str, field_name: str) -> bool:
        """
        检查代码行是否使用了指定字段
        """
        if not line_content or not field_name:
            return False

        # 常见的字段使用模式
        patterns = [
            f"{field_name}.",      # fieldName.method()
            f"{field_name}(",      # fieldName(args)
            f"({field_name})",     # (fieldName)
            f" {field_name} ",     # 空格包围
            f"={field_name}",      # =fieldName
            f"{field_name}=",      # fieldName=
            f"[{field_name}]",     # [fieldName]
        ]

        line_lower = line_content.lower()
        field_lower = field_name.lower()

        return any(pattern.lower() in line_lower for pattern in [
            f"{field_lower}.",
            f"{field_lower}(",
            f"({field_lower})",
            f" {field_lower} ",
            f"={field_lower}",
            f"{field_lower}=",
            f"[{field_lower}]",
        ])

    def _find_method_calls_in_class(self, class_info: Dict[str, Any], file_path: str,
                                   target_class_name: str, target_methods: List[str]) -> List[Dict[str, Any]]:
        """
        查找类中对目标类方法的调用
        """
        method_calls = []

        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                file_content = f.read()
                lines = file_content.split('\n')
        except Exception:
            return method_calls

        simple_target_name = target_class_name.split('.')[-1]

        # 在每个方法中查找目标类的方法调用
        for method in class_info.get("methods", []):
            method_name = method.get("name", "")
            method_start_line = method.get("start_line", 0)
            method_end_line = method.get("end_line", method_start_line + 10)

            # 分析方法体中的代码
            for line_num in range(max(0, method_start_line - 1), min(len(lines), method_end_line)):
                line_content = lines[line_num].strip()

                # 查找目标类的方法调用模式
                if self._contains_target_method_call(line_content, simple_target_name, target_methods):
                    called_method = self._extract_called_method(line_content, simple_target_name, target_methods)

                    method_calls.append({
                        "calling_method": method_name,
                        "called_method": called_method,
                        "line": line_num + 1
                    })

        return method_calls

    def _check_method_calls_in_class(self, class_info: Dict[str, Any], file_path: str,
                                   class_name: str, target_class_name: str, target_methods: List[str],
                                   suggest: str, problemtype: str, severity: str) -> List[Dict[str, Any]]:
        """
        检查类中的方法调用
        """
        issues = []

        # 获取类的源代码内容用于分析方法调用
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                file_content = f.read()
                lines = file_content.split('\n')
        except Exception:
            return issues

        # 简单类名（去掉包名）
        simple_target_name = target_class_name.split('.')[-1]

        # 在每个方法中查找目标类的方法调用
        for method in class_info.get("methods", []):
            method_name = method.get("name", "")
            method_start_line = method.get("start_line", 0)
            method_end_line = method.get("end_line", method_start_line + 10)

            # 分析方法体中的代码
            for line_num in range(max(0, method_start_line - 1), min(len(lines), method_end_line)):
                line_content = lines[line_num].strip()

                # 查找目标类的方法调用模式
                # 例如: topology1Service.someMethod() 或 Topology1Service.staticMethod()
                if self._contains_target_method_call(line_content, simple_target_name, target_methods):
                    # 确定具体调用的方法名
                    called_method = self._extract_called_method(line_content, simple_target_name, target_methods)

                    issues.append({
                        "file": file_path,
                        "error_type": problemtype,         # 交换：使用 problemtype 作为 error_type
                        "error_code": "target_detection",  # 交换：使用 target_detection 作为 error_code
                        "category": "target_detection",
                        "severity": severity,
                        "message": f"在方法 '{method_name}' 中调用了废弃的 '{target_class_name}.{called_method}()'",
                        "line": line_num + 1,  # 转换为1基索引
                        "element": "method_call",
                        "element_name": f"{method_name} -> {called_method}",
                        "class_name": class_name,
                        "target_class": target_class_name,
                        "called_method": called_method,
                        "calling_method": method_name,
                        "suggest": suggest,
                        "problemtype": problemtype,
                        "rule_type": "target_detection_call"
                    })

        return issues

    def _contains_target_method_call(self, line_content: str, target_class_name: str, target_methods: List[str]) -> bool:
        """
        检查代码行是否包含目标类的方法调用
        """
        if not line_content or not target_class_name:
            return False

        # 如果没有指定具体方法，检查任何对该类的调用
        if not target_methods:
            # 查找 targetClass.anyMethod() 或 TargetClass.anyMethod() 模式
            lower_class_name = target_class_name.lower()
            patterns = [
                f"{lower_class_name}.",  # topology1service.
                f"{target_class_name}.",  # Topology1Service.
            ]
            return any(pattern in line_content.lower() for pattern in patterns)

        # 如果指定了具体方法，只检查这些方法
        for method in target_methods:
            # 构建多种可能的调用模式
            patterns = [
                f"{target_class_name.lower()}.{method.lower()}(",  # topology1service.gettopologydata(
                f"{target_class_name}.{method}(",                  # Topology1Service.getTopologyData(
                # 处理驼峰命名的字段名
                f"{target_class_name[0].lower() + target_class_name[1:]}.{method}(",  # topology1Service.getTopologyData(
            ]

            line_lower = line_content.lower()
            for pattern in patterns:
                if pattern.lower() in line_lower:
                    return True

        return False

    def _extract_called_method(self, line_content: str, target_class_name: str, target_methods: List[str]) -> str:
        """
        从代码行中提取被调用的方法名
        """
        import re

        # 如果指定了具体方法，优先匹配这些方法
        if target_methods:
            for method in target_methods:
                # 多种匹配模式
                patterns = [
                    rf"{target_class_name.lower()}\.{method.lower()}\s*\(",  # topology1service.gettopologydata(
                    rf"{target_class_name}\.{method}\s*\(",                  # Topology1Service.getTopologyData(
                    rf"{target_class_name[0].lower() + target_class_name[1:]}\.{method}\s*\(",  # topology1Service.getTopologyData(
                ]

                for pattern in patterns:
                    if re.search(pattern, line_content, re.IGNORECASE):
                        return method

        # 通用方法提取：查找 .methodName( 模式
        patterns = [
            rf"{target_class_name.lower()}\.(\w+)\s*\(",
            rf"{target_class_name}\.(\w+)\s*\(",
            rf"{target_class_name[0].lower() + target_class_name[1:]}\.(\w+)\s*\(",
        ]

        for pattern in patterns:
            match = re.search(pattern, line_content, re.IGNORECASE)
            if match:
                return match.group(1)

        return "unknown"

    def _check_regex_patterns(self, class_info: Dict[str, Any], file_path: str, class_name: str) -> List[Dict[str, Any]]:
        """
        检查正则表达式模式匹配规则
        """
        issues = []

        # 获取正则匹配规则
        regex_rules = self.rules.get("regex_pattern_rules", {})
        if not regex_rules.get("enabled", False):
            return issues

        patterns = regex_rules.get("patterns", [])
        if not patterns:
            return issues

        # 读取文件内容进行正则匹配
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                file_content = f.read()
                lines = file_content.split('\n')
        except Exception as e:
            print(f"警告: 无法读取文件 {file_path}: {e}")
            return issues

        for pattern_rule in patterns:
            pattern_name = pattern_rule.get("name", "")
            pattern = pattern_rule.get("pattern", "")
            severity = pattern_rule.get("severity", "warning")
            suggest = pattern_rule.get("suggest", "")
            problemtype = pattern_rule.get("problemtype", "")
            message_template = pattern_rule.get("message_template", "发现已废弃的方法调用: {pattern_name} (行: {line})")

            if not pattern:
                continue

            import re
            try:
                # 支持跨行匹配 - 对整个文件内容进行匹配
                matches = re.finditer(pattern, file_content, re.DOTALL)
                for match in matches:
                    # 计算匹配位置的行号
                    match_start = match.start()
                    match_end = match.end()

                    # 计算起始行号
                    start_line = file_content[:match_start].count('\n') + 1
                    end_line = file_content[:match_end].count('\n') + 1

                    # 获取匹配的文本（清理换行符用于显示）
                    matched_text = match.group(0)
                    matched_text_clean = ' '.join(matched_text.split())

                    # 检查是否在注释中（检查匹配开始位置所在的行）
                    start_line_content = lines[start_line - 1] if start_line <= len(lines) else ""
                    match_pos_in_line = match_start - file_content[:match_start].rfind('\n') - 1
                    if match_pos_in_line < 0:
                        match_pos_in_line = match_start

                    if self._is_in_comment(start_line_content, match_pos_in_line):
                        continue

                    issues.append({
                        "file": file_path,
                        "type": "structure_check",
                        "category": "regex_pattern",
                        "severity": severity,
                        "message": message_template.format(pattern_name=pattern_name, line=start_line),
                        "line": start_line,
                        "end_line": end_line,
                        "element": "pattern_match",
                        "element_name": pattern_name,
                        "class_name": class_name,
                        "matched_text": matched_text_clean,
                        "pattern": pattern,
                        "suggest": suggest,
                        "problemtype": problemtype,
                        "rule_type": "regex_pattern_match"
                    })

            except re.error as e:
                print(f"警告: 正则表达式 '{pattern}' 有误: {e}")
                continue

        return issues

    def _is_in_comment(self, line_content: str, position: int) -> bool:
        """
        检查指定位置是否在注释中
        """
        # 简单检查：如果行以//开头或者位置前有//，则认为在注释中
        if line_content.strip().startswith('//'):
            return True

        # 检查位置前是否有//
        if position < len(line_content):
            before_position = line_content[:position]
            if '//' in before_position:
                return True

        return False

    def export_issues(self, output_file: str = "structure_issues.json") -> None:
        """
        导出结构问题到文件

        Args:
            output_file: 输出文件路径
        """
        print(f"导出结构问题到文件: {output_file}")

        # 按严重程度分类统计
        severity_count = {}
        category_count = {}
        rule_type_count = {}

        for issue in self.issues:
            severity = issue.get("severity", "unknown")
            category = issue.get("category", "unknown")
            rule_type = issue.get("rule_type", "unknown")

            severity_count[severity] = severity_count.get(severity, 0) + 1
            category_count[category] = category_count.get(category, 0) + 1
            rule_type_count[rule_type] = rule_type_count.get(rule_type, 0) + 1

        # 构建输出数据
        output_data = {
            "scan_info": {
                "timestamp": datetime.now().isoformat(),
                "total_issues": len(self.issues),
                "severity_summary": severity_count,
                "category_summary": category_count,
                "rule_type_summary": rule_type_count,
                "scan_type": "structure_check"
            },
            "rules_applied": {
                "method_return_type_rules": self.rules.get("method_return_type_rules", {}),
                "annotation_issues_rules": self.rules.get("annotation_issues_rules", {}),
                "target_detection_rules": self.rules.get("target_detection_rules", {})
            },
            "issues": self.issues
        }

        # 写入JSON文件
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(output_data, f, ensure_ascii=False, indent=2)

        print(f"结构问题检查完成！结果已保存到 {output_file}")
        print(f"问题统计:")
        print(f"  - 发现问题数: {len(self.issues)}")
        for severity, count in severity_count.items():
            print(f"  - {severity}: {count}")
