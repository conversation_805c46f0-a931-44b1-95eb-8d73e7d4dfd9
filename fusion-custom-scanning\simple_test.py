import subprocess
import os

def main():
    print("=== 开始执行CLI测试 ===")
    
    # 步骤1: 显示当前目录和上一层级目录
    current_dir = os.getcwd()
    parent_dir = os.path.dirname(current_dir)
    print(f"当前目录: {current_dir}")
    print(f"切换到上一层级: {parent_dir}")
    
    # 步骤2: 尝试启动Qwen
    print("\n--- 尝试启动Qwen ---")
    try:
        process = subprocess.Popen(
            ['Qwen'], 
            stdin=subprocess.PIPE, 
            stdout=subprocess.PIPE, 
            stderr=subprocess.PIPE,
            text=True,
            cwd=parent_dir
        )
        
        # 步骤3: 发送test输入
        print("向Qwen发送输入: test")
        stdout, stderr = process.communicate(input='test\n', timeout=10)
        
        print(f"Qwen返回码: {process.returncode}")
        print(f"Qwen输出: {stdout}")
        if stderr:
            print(f"Qwen错误: {stderr}")
            
    except FileNotFoundError:
        print("Qwen命令未找到")
    except subprocess.TimeoutExpired:
        print("Qwen执行超时")
        process.kill()
    except Exception as e:
        print(f"Qwen执行异常: {e}")
    
    # 步骤4: 使用替代命令测试目录切换功能
    print("\n--- 使用dir命令测试目录切换 ---")
    try:
        process = subprocess.Popen(
            ['cmd', '/c', 'dir'],
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            text=True,
            cwd=parent_dir
        )
        
        stdout, stderr = process.communicate()
        print(f"dir命令返回码: {process.returncode}")
        print("上一层级目录内容:")
        print(stdout[:500])  # 只显示前500个字符
        
    except Exception as e:
        print(f"dir命令执行异常: {e}")
    
    print("\n=== 测试完成 ===")

if __name__ == "__main__":
    main()
