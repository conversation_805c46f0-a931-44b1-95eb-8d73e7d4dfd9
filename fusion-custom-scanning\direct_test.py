import subprocess
import os

def main():
    print("=== 直接执行用户要求的命令序列 ===")
    
    # 获取目录信息
    current_dir = os.getcwd()
    parent_dir = os.path.dirname(current_dir)
    
    print(f"当前目录: {current_dir}")
    print(f"上级目录: {parent_dir}")
    
    # 步骤1: cd ../
    print(f"\n1. 执行 cd ../")
    try:
        result = subprocess.run(['cmd', '/c', 'cd .. && cd'], 
                              capture_output=True, text=True, cwd=current_dir)
        print(f"   结果: {result.stdout.strip()}")
    except Exception as e:
        print(f"   错误: {e}")
    
    # 步骤2: 检查Qwen
    print(f"\n2. 检查Qwen命令")
    try:
        result = subprocess.run(['where', 'qwen'], capture_output=True, text=True)
        if result.returncode == 0:
            print(f"   找到: {result.stdout.strip()}")
        else:
            print("   未找到qwen命令")
    except Exception as e:
        print(f"   错误: {e}")
    
    # 步骤3: 在上级目录执行Qwen
    print(f"\n3. 在上级目录执行Qwen并发送test")
    try:
        process = subprocess.Popen(['qwen'], 
                                 stdin=subprocess.PIPE, 
                                 stdout=subprocess.PIPE, 
                                 stderr=subprocess.PIPE,
                                 text=True, cwd=parent_dir)
        
        stdout, stderr = process.communicate(input='test\n', timeout=10)
        
        print(f"   返回码: {process.returncode}")
        print(f"   输出: {stdout}")
        if stderr:
            print(f"   错误: {stderr}")
            
    except FileNotFoundError:
        print("   Qwen命令未找到")
    except subprocess.TimeoutExpired:
        print("   Qwen执行超时")
        process.kill()
    except Exception as e:
        print(f"   异常: {e}")
    
    print(f"\n=== 测试完成 ===")

if __name__ == "__main__":
    main()
