import subprocess
import os
import time


def run_code_processing_task():
    """假设这是你的一系列代码处理任务"""
    result_a = "result from code processing"
    return result_a


def test_qwen_cli(command='Qwen', test_input='test'):
    """
    按照要求执行以下步骤：
    1. cd ../ (返回上一层级)
    2. <PERSON><PERSON> (启动Qwen CLI工具)
    3. 输入test，返回输出结果

    参数:
    command: 要执行的命令 (默认: 'Qwen')
    test_input: 要发送给命令的输入 (默认: 'test')
    """
    try:
        # 步骤1: 获取当前目录并切换到上一层级
        current_dir = os.getcwd()
        parent_dir = os.path.dirname(current_dir)
        print(f"当前目录: {current_dir}")
        print(f"切换到上一层级: {parent_dir}")

        # 步骤2: 在上一层级目录启动CLI工具
        print(f"启动{command} CLI工具...")
        process = subprocess.Popen(
            [command],
            stdin=subprocess.PIPE,
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            text=True,
            cwd=parent_dir  # 在上一层级目录执行
        )

        # 步骤3: 输入测试内容并获取输出
        print(f"向{command}发送输入: {test_input}")
        stdout, stderr = process.communicate(input=f'{test_input}\n', timeout=30)

        if process.returncode == 0:
            print(f"{command}输出结果:")
            print(stdout)
            return stdout.strip()
        else:
            print(f"{command}执行出错 (返回码: {process.returncode}):")
            print(f"错误信息: {stderr}")
            return None

    except subprocess.TimeoutExpired:
        print(f"{command}执行超时")
        process.kill()
        return None
    except FileNotFoundError:
        print(f"错误: 找不到{command}命令，请确保{command}已正确安装并在PATH中")
        return None
    except Exception as e:
        print(f"执行过程中发生错误: {e}")
        return None


def test_with_alternative_command():
    """使用替代命令进行测试（如果Qwen不可用）"""
    print("\n=== 尝试使用替代命令进行测试 ===")

    # 测试echo命令（Windows和Linux都有）
    try:
        current_dir = os.getcwd()
        parent_dir = os.path.dirname(current_dir)
        print(f"当前目录: {current_dir}")
        print(f"切换到上一层级: {parent_dir}")

        # 在Windows上使用echo命令
        if os.name == 'nt':  # Windows
            process = subprocess.Popen(
                ['cmd', '/c', 'echo', 'Hello from parent directory'],
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                text=True,
                cwd=parent_dir
            )
        else:  # Unix/Linux
            process = subprocess.Popen(
                ['echo', 'Hello from parent directory'],
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                text=True,
                cwd=parent_dir
            )

        stdout, stderr = process.communicate()

        if process.returncode == 0:
            print("替代命令输出结果:")
            print(stdout)
            return stdout.strip()
        else:
            print(f"替代命令执行出错: {stderr}")
            return None

    except Exception as e:
        print(f"替代命令执行失败: {e}")
        return None


def call_cli_tool_multi_step(input_a, input_b):
    """原有的多步骤CLI工具调用函数（保留作为备用）"""
    try:
        # 启动 CLI 工具
        process = subprocess.Popen(['Qwen'], stdin=subprocess.PIPE, stdout=subprocess.PIPE, stderr=subprocess.PIPE,
                                   text=True)

        # 发送第一个输入并读取输出
        stdout, stderr = process.communicate(input=input_a + '\n')
        if process.returncode != 0:
            print("Error during first input:", stderr)
            return None

        # 处理第一步的输出，准备第二步的输入
        second_input = f"Next input based on output: {stdout.strip()}"  # 修改为适合您的逻辑

        # 发送第二个输入
        stdout, stderr = process.communicate(input=second_input + '\n')
        if process.returncode != 0:
            print("Error during second input:", stderr)
            return None

        # 获取最终结果
        return stdout.strip()
    except Exception as e:
        print(f"调用CLI工具时发生错误: {e}")
        return None


def main():
    print("=== 开始执行Qwen CLI测试 ===")
    print("测试步骤:")
    print("1. cd ../ (返回上一层级)")
    print("2. Qwen (启动Qwen CLI工具)")
    print("3. 输入test，返回输出结果")
    print("-" * 50)

    # 执行新的测试流程
    result = test_qwen_cli()

    if result is not None:
        print("\n=== Qwen测试完成，结果如下 ===")
        print(result)
    else:
        print("\n=== Qwen测试失败，尝试替代方案验证功能 ===")
        # 如果Qwen不可用，尝试替代命令验证目录切换功能
        alt_result = test_with_alternative_command()
        if alt_result is not None:
            print("\n=== 替代测试完成，证明目录切换功能正常 ===")
            print("注意: 如果需要测试Qwen，请确保:")
            print("1. Qwen已正确安装")
            print("2. Qwen命令在系统PATH中")
            print("3. 或者修改脚本中的命令路径")
        else:
            print("\n=== 所有测试都失败了 ===")

    print("\n=== 程序结束 ===")


if __name__ == "__main__":
    main()
