import subprocess


def run_code_processing_task():
    # 假设这是你的一系列代码处理任务
    result_a = "result from code processing"
    return result_a


def call_cli_tool_multi_step(input_a, input_b):
    # 启动 CLI 工具
    process = subprocess.Popen(['Qwen'], stdin=subprocess.PIPE, stdout=subprocess.PIPE, stderr=subprocess.PIPE,
                               text=True)

    # 发送第一个输入并读取输出
    stdout, stderr = process.communicate(input=input_a + '\n')
    if process.returncode != 0:
        print("Error during first input:", stderr)
        return None

    # 处理第一步的输出，准备第二步的输入
    second_input = f"Next input based on output: {stdout.strip()}"  # 修改为适合您的逻辑

    # 发送第二个输入
    stdout, stderr = process.communicate(input=second_input + '\n')
    if process.returncode != 0:
        print("Error during second input:", stderr)
        return None

    # 获取最终结果
    return stdout.strip()


def main():
    # 第一步：处理代码任务
    result_a = run_code_processing_task()

    # 第二步：调用 CLI 工具
    result_b = call_cli_tool_multi_step("cd../", "ll")

    # # 第三步：合并结果并输出
    # if result_b is not None:
    #     combined_result = f"{result_a}\n{result_b}"
    #     print("Combined Results:\n", combined_result)


if __name__ == "__main__":
    main()
