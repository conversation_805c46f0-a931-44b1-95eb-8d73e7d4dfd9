import subprocess
import os
import time

def execute_sequential_commands():
    """
    按照用户要求的顺序执行命令：
    1. cd ../ (返回上一层级)
    2. <PERSON>wen (启动Qwen CLI工具)
    3. 输入test，返回输出结果
    """
    print("=== 开始按顺序执行控制台命令 ===")
    print("命令序列:")
    print("1. cd ../")
    print("2. Qwen")
    print("3. 输入: test")
    print("-" * 50)
    
    try:
        # 启动一个交互式的cmd进程
        print("启动交互式命令行...")
        process = subprocess.Popen(
            ['cmd'],
            stdin=subprocess.PIPE,
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            text=True,
            bufsize=0  # 无缓冲，实时输出
        )
        
        # 命令1: cd ../
        print("\n>>> 执行命令: cd ../")
        process.stdin.write('cd ..\n')
        process.stdin.flush()
        time.sleep(0.5)  # 等待命令执行
        
        # 命令2: <PERSON>wen
        print(">>> 执行命令: Qwen")
        process.stdin.write('Qwen\n')
        process.stdin.flush()
        time.sleep(1)  # 等待Qwen启动
        
        # 命令3: 输入test
        print(">>> 发送输入: test")
        process.stdin.write('test\n')
        process.stdin.flush()
        time.sleep(2)  # 等待处理
        
        # 发送exit退出
        print(">>> 发送退出命令")
        process.stdin.write('exit\n')
        process.stdin.flush()
        
        # 等待进程结束并获取输出
        stdout, stderr = process.communicate(timeout=10)
        
        print("\n=== 命令执行完成 ===")
        print("标准输出:")
        print(stdout)
        
        if stderr:
            print("\n错误输出:")
            print(stderr)
            
        return stdout, stderr
        
    except subprocess.TimeoutExpired:
        print("命令执行超时")
        process.kill()
        return None, "超时"
    except Exception as e:
        print(f"执行过程中发生错误: {e}")
        return None, str(e)

def execute_with_shell_sequence():
    """
    使用shell命令序列的方式执行
    """
    print("\n=== 使用shell命令序列方式 ===")
    
    try:
        # 方式1: 尝试在一个命令中执行序列
        print("尝试执行: cd .. && Qwen")
        process = subprocess.Popen(
            ['cmd', '/c', 'cd .. && echo 当前目录: && cd && echo 尝试启动Qwen: && Qwen'],
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            text=True
        )
        
        stdout, stderr = process.communicate(timeout=10)
        
        print("命令序列输出:")
        print(stdout)
        
        if stderr:
            print("错误输出:")
            print(stderr)
            
        return stdout, stderr
        
    except Exception as e:
        print(f"shell序列执行失败: {e}")
        return None, str(e)

def test_step_by_step():
    """
    分步测试每个命令
    """
    print("\n=== 分步测试每个命令 ===")
    
    # 步骤1: 测试cd命令
    print("1. 测试cd ../命令:")
    try:
        result = subprocess.run(
            ['cmd', '/c', 'cd .. && cd'],
            capture_output=True,
            text=True,
            timeout=5
        )
        print(f"   cd命令结果: {result.stdout.strip()}")
    except Exception as e:
        print(f"   cd命令失败: {e}")
    
    # 步骤2: 测试在上级目录执行命令
    print("\n2. 在上级目录测试命令:")
    current_dir = os.getcwd()
    parent_dir = os.path.dirname(current_dir)
    print(f"   当前目录: {current_dir}")
    print(f"   上级目录: {parent_dir}")
    
    try:
        # 在上级目录执行dir命令验证
        result = subprocess.run(
            ['cmd', '/c', 'dir'],
            capture_output=True,
            text=True,
            cwd=parent_dir,
            timeout=5
        )
        print("   上级目录内容 (前3行):")
        lines = result.stdout.split('\n')[:3]
        for line in lines:
            if line.strip():
                print(f"   {line}")
    except Exception as e:
        print(f"   目录测试失败: {e}")
    
    # 步骤3: 测试Qwen命令
    print("\n3. 测试Qwen命令:")
    try:
        result = subprocess.run(
            ['Qwen', '--help'],
            capture_output=True,
            text=True,
            timeout=5,
            cwd=parent_dir
        )
        print("   Qwen命令可用")
        print(f"   帮助信息: {result.stdout[:100]}...")
    except FileNotFoundError:
        print("   Qwen命令未找到")
    except Exception as e:
        print(f"   Qwen测试失败: {e}")

def main():
    print("=== Python控制台命令顺序执行测试 ===")
    print("目标: 按照以下顺序执行命令")
    print("1. cd ../")
    print("2. Qwen")  
    print("3. 输入test")
    print("=" * 60)
    
    # 方法1: 交互式执行
    result1 = execute_sequential_commands()
    
    # 方法2: shell命令序列
    result2 = execute_with_shell_sequence()
    
    # 方法3: 分步测试
    test_step_by_step()
    
    print("\n" + "=" * 60)
    print("=== 所有测试完成 ===")

if __name__ == "__main__":
    main()
