#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
精确类查找器 - 基于Maven classpath在实际JAR包中查找类
结合problems文件分析，提供准确的修复建议
"""

import os
import subprocess
import zipfile
import xml.etree.ElementTree as ET
import sys
import re
import json
from pathlib import Path
from typing import List, Dict, Any, Optional
from .vector_search import get_vector_search_service, VectorSearchResult, process_vector_search_results


class PreciseClassFinder:
    def __init__(self, problems_file, project_path, mvn_executable="mvn"):
        self.problems_file = problems_file
        self.project_path = Path(project_path)
        self.mvn_executable = mvn_executable
        self.missing_classes = []
        self.jar_paths = []

    def analyze(self):
        """执行完整分析"""
        print("精确类查找分析开始...")

        # 1. 从problems文件提取缺失的类
        self._extract_missing_classes()
        print(f"从problems文件中提取到 {len(self.missing_classes)} 个缺失的类")

        # 2. 分析通配符导入问题
        wildcard_analysis = self._analyze_wildcard_imports()

        # 3. 构建Maven classpath
        self._build_maven_classpath()
        print(f"Maven classpath包含 {len(self.jar_paths)} 个JAR包")

        # 4. 在JAR包中搜索类
        found_classes = self._search_classes_in_jars()

        # 4.5. 向量搜索相似类（新增功能）
        vector_search_results = self._search_similar_classes_by_vector(wildcard_analysis)

        # 5. 分析结果并生成建议（统一处理JAR搜索和向量搜索结果）
        analysis_result = self._generate_analysis_result(found_classes, wildcard_analysis, vector_search_results)

        return analysis_result

    def _extract_missing_classes(self):
        """从problems文件中提取缺失的类名"""
        try:
            # 尝试不同的编码
            encodings = ['utf-8', 'cp936', 'gbk', 'gb2312']
            content = None
            for encoding in encodings:
                try:
                    with open(self.problems_file, 'r', encoding=encoding) as f:
                        content = f.read()
                    break
                except UnicodeDecodeError:
                    continue

            if content is None:
                raise Exception("无法解析文件编码")

            root = ET.fromstring(content)

            # 提取Java文件信息
            first_problem = root.find('.//problem')
            if first_problem is not None:
                file_elem = first_problem.find('file')
                module_elem = first_problem.find('module')
                package_elem = first_problem.find('package')

                self.java_file_info = {
                    'file': file_elem.text.strip() if file_elem is not None and file_elem.text else '',
                    'module': module_elem.text.strip() if module_elem is not None and module_elem.text else '',
                    'package': package_elem.text.strip() if package_elem is not None and package_elem.text else ''
                }
            else:
                self.java_file_info = {'file': '', 'module': '', 'package': ''}

            # 分别存储不同entry_point类型的问题
            file_type_classes = {}  # entry_point TYPE="file"的问题
            method_class_type_classes = {}  # entry_point TYPE="method"和"class"的问题

            # 首先处理entry_point TYPE="file"的问题
            for problem in root.findall('.//problem'):
                description = problem.find('description')
                highlighted = problem.find('highlighted_element')
                line = problem.find('line')
                entry_point = problem.find('entry_point')

                if description is not None and highlighted is not None:
                    desc_text = description.text or ''
                    highlighted_text = highlighted.text or ''
                    line_num = int(line.text) if line is not None else 0

                    # 获取entry_point类型
                    entry_point_type = entry_point.get('TYPE') if entry_point is not None else None

                    # 关注"未解析的引用"问题，包括类名和包符号
                    if '未解析的引用' in desc_text and highlighted_text:
                        # 判断是否是类（首字母大写）但尚未处理过
                        is_class = highlighted_text[0].isupper() if highlighted_text else False
                        class_name = highlighted_text if is_class else None

                        # 如果是类且entry_point类型为"file"，记录到file_type_classes
                        if is_class and entry_point_type == 'file':
                            if class_name in file_type_classes:
                                # 如果已经处理过，添加新的行号
                                file_type_classes[class_name]['lines'].append(line_num)
                            else:
                                # 如果未处理过，记录该类
                                file_type_classes[class_name] = {
                                    'highlighted_element': highlighted_text,
                                    'lines': [line_num],
                                    'description': desc_text,
                                    'is_class': is_class,
                                    'entry_point_type': entry_point_type
                                }

            # 然后处理entry_point TYPE="method"和"class"的问题
            for problem in root.findall('.//problem'):
                description = problem.find('description')
                highlighted = problem.find('highlighted_element')
                line = problem.find('line')
                entry_point = problem.find('entry_point')

                if description is not None and highlighted is not None:
                    desc_text = description.text or ''
                    highlighted_text = highlighted.text or ''
                    line_num = int(line.text) if line is not None else 0

                    # 获取entry_point类型
                    entry_point_type = entry_point.get('TYPE') if entry_point is not None else None

                    # 关注"未解析的引用"问题，包括类名和包符号
                    if '未解析的引用' in desc_text and highlighted_text:
                        # 判断是否是类（首字母大写）但尚未处理过
                        is_class = highlighted_text[0].isupper() if highlighted_text else False
                        class_name = highlighted_text if is_class else None

                        # 如果是类且entry_point类型为"method"或"class"
                        if is_class and entry_point_type in ['method', 'class']:
                            # 检查是否在file_type_classes中已经存在
                            if class_name in file_type_classes:
                                # 如果已存在，添加行号到已有的记录中
                                file_type_classes[class_name]['lines'].append(line_num)
                            else:
                                # 如果不存在，检查是否在method_class_type_classes中
                                if class_name in method_class_type_classes:
                                    # 如果已存在，添加行号
                                    method_class_type_classes[class_name]['lines'].append(line_num)
                                else:
                                    # 如果未处理过，记录该类
                                    method_class_type_classes[class_name] = {
                                        'highlighted_element': highlighted_text,
                                        'lines': [line_num],
                                        'description': desc_text,
                                        'is_class': is_class,
                                        'entry_point_type': entry_point_type
                                    }

            # 将处理过的类添加到missing_classes中
            # 首先添加file_type_classes
            for class_name, class_info in file_type_classes.items():
                self.missing_classes.append({
                    'class_name': class_name,
                    'highlighted_element': class_info['highlighted_element'],
                    'line': class_info['lines'][0],  # 使用第一个行号作为主要行号
                    'lines': class_info['lines'],  # 保存所有行号
                    'description': class_info['description'],
                    'is_class': class_info['is_class'],
                    'entry_point_type': class_info['entry_point_type']
                })

            # 然后添加method_class_type_classes
            for class_name, class_info in method_class_type_classes.items():
                self.missing_classes.append({
                    'class_name': class_name,
                    'highlighted_element': class_info['highlighted_element'],
                    'line': class_info['lines'][0],  # 使用第一个行号作为主要行号
                    'lines': class_info['lines'],  # 保存所有行号
                    'description': class_info['description'],
                    'is_class': class_info['is_class'],
                    'entry_point_type': class_info['entry_point_type']
                })

        except Exception as e:
            print(f"解析problems文件失败: {e}")
            self.java_file_info = {'file': '', 'module': '', 'package': ''}

    def _build_maven_classpath(self):
        """构建Maven classpath"""
        # 将classpath.txt输出到分析工具目录（main.py所在目录）
        tool_dir = Path(__file__).parent.parent  # 从service目录回到main.py目录
        classpath_file = tool_dir / "classpath.txt"

        # 尝试不同的Maven可执行文件
        maven_candidates = [
            self.mvn_executable,
            r"E:\maven\apache-maven-3.6.3\bin\mvn.cmd",  # 用户指定的Maven路径
            "mvn.cmd",
            "mvn.bat",
            "mvn",
            r"C:\Program Files\Apache\maven\bin\mvn.cmd",
            r"D:\apache-maven-3.3.9\bin\mvn.cmd"
        ]

        working_mvn = None
        for mvn_cmd in maven_candidates:
            try:
                # 测试Maven是否可用
                test_proc = subprocess.run(
                    [mvn_cmd, "--version"],
                    stdout=subprocess.PIPE,
                    stderr=subprocess.PIPE,
                    timeout=10
                )
                if test_proc.returncode == 0:
                    working_mvn = mvn_cmd
                    print(f"找到可用的Maven: {mvn_cmd}")
                    # 显示Maven版本信息
                    version_info = test_proc.stdout.strip().split('\n')[0] if test_proc.stdout else "未知版本"
                    print(f"Maven版本: {version_info}")
                    break
            except:
                continue

        if not working_mvn:
            print("未找到可用的Maven命令，尝试分析现有依赖...")
            self._analyze_existing_dependencies()
            return

        # 构建Maven命令
        cmd = [
            working_mvn,
            "dependency:build-classpath",
            "-DincludeScope=compile",
            f"-Dmdep.outputFile={classpath_file}",
            "-Dmdep.regenerateFile=true",  # 强制重新生成文件
            "-Dmaven.main.skip=true",
            "-Dmaven.test.skip=true"
        ]

        print(f"执行Maven命令: {' '.join(cmd)}")
        print(f"工作目录: {self.project_path}")

        try:
            proc = subprocess.run(
                cmd,
                cwd=self.project_path,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                text=True,
                timeout=120  # 2分钟超时
            )

            print(f"Maven命令返回码: {proc.returncode}")

            if proc.stdout:
                print("Maven输出:")
                print(proc.stdout)

            if proc.stderr:
                print("Maven错误输出:")
                print(proc.stderr)

            if proc.returncode != 0:
                print("Maven执行失败，尝试备选方案...")
                self._analyze_existing_dependencies()
                return

            # 检查多个可能的classpath文件位置
            possible_locations = [
                classpath_file,  # 分析工具目录
                self.project_path / "classpath.txt",  # 项目根目录
                self.project_path / self.project_path.name / "classpath.txt",  # 嵌套目录
                self.project_path / "target" / "classpath.txt"  # target目录
            ]

            actual_classpath_file = None
            for location in possible_locations:
                if location.exists():
                    actual_classpath_file = location
                    print(f"找到classpath文件: {actual_classpath_file}")
                    break

            if not actual_classpath_file:
                print(f"classpath.txt文件未找到，检查了以下位置:")
                for loc in possible_locations:
                    print(f"  - {loc}")
                print("尝试备选方案...")
                self._analyze_existing_dependencies()
                return

            classpath_file = actual_classpath_file

            # 读取classpath
            with open(classpath_file, "r", encoding="utf-8") as f:
                classpath_content = f.read().strip()
                if classpath_content:
                    self.jar_paths = classpath_content.split(os.pathsep)
                    # 过滤掉空路径
                    self.jar_paths = [jar for jar in self.jar_paths if jar.strip()]

        except subprocess.TimeoutExpired:
            print("Maven命令执行超时")
        except Exception as e:
            print(f"执行Maven命令失败: {e}")
            print("尝试分析现有依赖...")
            self._analyze_existing_dependencies()

    def _analyze_existing_dependencies(self):
        """分析现有的依赖（当Maven不可用时的备选方案）"""
        print("Maven不可用，基于已知模式分析依赖...")

        # 模拟一些常见的JAR包路径（基于典型的Maven本地仓库结构）
        home_dir = Path.home()
        m2_repo = home_dir / ".m2" / "repository"

        if m2_repo.exists():
            print(f"找到Maven本地仓库: {m2_repo}")
            # 搜索一些常见的依赖
            common_deps = [
                "com/cet/electric/eem-auth",
                "com/cet/electric/eem-bll-common",
                "com/cet/electric/eem-common",
                "com/cet/electric/piem-entity",
                "io/springfox/springfox-swagger2",
                "org/springframework/spring-web"
            ]

            for dep_path in common_deps:
                full_dep_path = m2_repo / dep_path
                if full_dep_path.exists():
                    # 查找该依赖下的JAR文件
                    for jar_file in full_dep_path.rglob("*.jar"):
                        if not jar_file.name.endswith("-sources.jar") and not jar_file.name.endswith("-javadoc.jar"):
                            self.jar_paths.append(str(jar_file))
                            print(f"  找到JAR: {jar_file.name}")
        else:
            print("未找到Maven本地仓库，无法进行进一步分析")
            print("建议:")
            print("1. 确保Maven已正确安装并配置在PATH中")
            print("2. 或者指定Maven可执行文件的完整路径")
            print("3. 或者手动运行: mvn dependency:build-classpath")

    def _search_classes_in_jars(self):
        """搜索类（优先搜索当前工程，然后搜索JAR包）"""
        found_classes = {}

        # 获取唯一的类名列表（只包含真正的类名）
        unique_classes = list(set(cls['class_name'] for cls in self.missing_classes if cls['class_name'] and cls['is_class']))

        print(f"开始搜索 {len(unique_classes)} 个类...")
        print(f"搜索范围: 当前工程源码 + {len(self.jar_paths)} 个JAR包")

        for class_name in unique_classes:
            print(f"  搜索类: {class_name}")

            # 优先搜索当前工程
            project_results = self._search_in_current_project(class_name)
            jar_results = self._search_single_class(class_name)

            # 合并结果，当前工程的结果优先
            all_results = project_results + jar_results

            if all_results:
                found_classes[class_name] = all_results
                project_count = len(project_results)
                jar_count = len(jar_results)
                print(f"    ✓ 找到 {len(all_results)} 个匹配 (当前工程: {project_count}, JAR包: {jar_count})")
            else:
                print(f"    ✗ 未找到")

        return found_classes

    def _search_in_current_project(self, class_name):
        """在当前工程源码中搜索类"""
        results = []

        # 搜索src/main/java目录
        src_dirs = [
            self.project_path / "src" / "main" / "java",
            self.project_path / ".." / "*/src/main/java"  # 多模块项目
        ]

        for src_pattern in src_dirs:
            if "*" in str(src_pattern):
                # 处理通配符路径（多模块项目）
                parent_dir = src_pattern.parent.parent
                if parent_dir.exists():
                    for module_dir in parent_dir.iterdir():
                        if module_dir.is_dir():
                            actual_src_dir = module_dir / "src" / "main" / "java"
                            if actual_src_dir.exists():
                                results.extend(self._search_in_source_directory(actual_src_dir, class_name))
            else:
                # 直接路径
                if src_pattern.exists():
                    results.extend(self._search_in_source_directory(src_pattern, class_name))

        return results

    def _search_in_source_directory(self, src_dir, class_name):
        """在指定源码目录中搜索类"""
        results = []

        # 递归搜索.java文件
        for java_file in src_dir.rglob(f"{class_name}.java"):
            try:
                # 从文件路径推断包名
                relative_path = java_file.relative_to(src_dir)
                package_parts = relative_path.parts[:-1]  # 去掉文件名
                package_name = ".".join(package_parts)
                full_class_name = f"{package_name}.{class_name}"

                # 只保留com.cet开头的类路径
                if full_class_name.startswith('com.cet'):
                    results.append({
                        'class_name': class_name,
                        'full_class_name': full_class_name,
                        'package_name': package_name,
                        'jar_name': '当前工程',
                        'jar_path': str(java_file),
                        'source': 'current_project'  # 标记为当前工程
                    })

            except Exception as e:
                # 忽略路径处理错误
                continue

        return results

    def _search_single_class(self, class_name):
        """搜索单个类，只返回com.cet开头的类路径"""
        results = []
        suffix = f"/{class_name}.class"

        for jar_path in self.jar_paths:
            if not os.path.exists(jar_path):
                continue

            try:
                with zipfile.ZipFile(jar_path, "r") as zipf:
                    for entry in zipf.namelist():
                        if entry.endswith(suffix):
                            # 转换为完整类名
                            full_class_name = entry.replace("/", ".").replace(".class", "")

                            # 只保留com.cet开头的类路径
                            if full_class_name.startswith('com.cet'):
                                jar_name = os.path.basename(jar_path)
                                package_name = ".".join(full_class_name.split(".")[:-1])

                                results.append({
                                    'class_name': class_name,
                                    'full_class_name': full_class_name,
                                    'package_name': package_name,
                                    'jar_name': jar_name,
                                    'jar_path': jar_path,
                                    'source': 'jar_dependency'  # 标记为JAR依赖
                                })

            except Exception as e:
                # 忽略损坏的ZIP文件
                continue

        return results

    def _analyze_wildcard_imports(self):
        """基于problems文件分析通配符导入问题"""
        java_file_path = self._get_java_file_path()
        if not java_file_path or not java_file_path.exists():
            return None

        try:
            # 1. 提取Java文件中的通配符导入及其行号
            wildcard_imports = self._extract_wildcard_imports_with_lines(java_file_path)

            if not wildcard_imports:
                return None

            print(f"发现 {len(wildcard_imports)} 个通配符导入:")
            for imp in wildcard_imports:
                print(f"  - 第{imp['line']}行: {imp['package']}.*")

            # 2. 基于problems中的包符号错误分析通配符导入问题
            return self._analyze_wildcard_package_problems(wildcard_imports)

        except Exception as e:
            print(f"分析通配符导入失败: {e}")

        return None

    def _extract_wildcard_imports_with_lines(self, java_file_path):
        """提取通配符导入及其行号"""
        wildcard_imports = []

        with open(java_file_path, 'r', encoding='utf-8') as f:
            lines = f.readlines()

        for line_num, line in enumerate(lines, 1):
            # 匹配通配符导入
            match = re.match(r'\s*import\s+([a-zA-Z_][a-zA-Z0-9_.]*)\.\*\s*;', line)
            if match:
                package = match.group(1)
                wildcard_imports.append({
                    'package': package,
                    'line': line_num,
                    'full_import': line.strip()
                })

        return wildcard_imports

    def _analyze_wildcard_package_problems(self, wildcard_imports):
        """基于problems文件中的包符号错误分析通配符导入问题"""
        issues = []

        # 按行号分组problems
        problems_by_line = {}
        for problem in self.missing_classes:
            line_num = problem['line']
            if line_num not in problems_by_line:
                problems_by_line[line_num] = []
            problems_by_line[line_num].append(problem)

        # 分析每个通配符导入
        for wildcard_import in wildcard_imports:
            wildcard_line = wildcard_import['line']
            wildcard_package = wildcard_import['package']

            print(f"检查通配符导入: 第{wildcard_line}行 {wildcard_package}.*")

            # 检查这一行是否有包符号问题
            if wildcard_line in problems_by_line:
                line_problems = problems_by_line[wildcard_line]
                print(f"  找到 {len(line_problems)} 个问题")

                # 检查是否有包符号无法解析的问题
                package_problems = []
                for problem in line_problems:
                    highlighted = problem.get('highlighted_element', '')
                    description = problem.get('description', '')

                    print(f"    问题: {description}, 高亮: {highlighted}")

                    # 检查是否是包符号问题（不是类名问题）
                    if '未解析的引用' in description and highlighted:
                        # 检查highlighted_element是否在通配符包路径中
                        # 支持各种包符号：classes, entity, dto, vo, common, utils等
                        package_parts = wildcard_package.split('.')
                        print(f"    包路径部分: {package_parts}")
                        if highlighted in package_parts:
                            print(f"    ✓ 找到包符号问题: {highlighted}")
                            package_problems.append({
                                'highlighted_element': highlighted,
                                'description': description,
                                'line': wildcard_line
                            })
                        else:
                            print(f"    ✗ {highlighted} 不在包路径中")

                if package_problems:
                    print(f"  ✓ 通配符导入有 {len(package_problems)} 个包符号问题")
                    # 这个通配符导入有包符号问题
                    issues.append({
                        'wildcard_import': wildcard_import,
                        'package_problems': package_problems
                    })
                else:
                    print(f"  ✗ 通配符导入没有包符号问题")
            else:
                print(f"  ✗ 第{wildcard_line}行没有找到问题")

        return issues



    def _generate_package_suggestions(self, affected_classes):
        """为受影响的类生成包路径建议"""
        # 按包分组类
        package_groups = {}

        for class_info in affected_classes:
            class_name = class_info['class_name']
            for result in class_info['correct_results']:
                package = result['package_name']
                if package not in package_groups:
                    package_groups[package] = []
                package_groups[package].append(class_name)

        # 生成建议
        suggestions = []
        for package, classes in package_groups.items():
            # 如果一个包有多个类，建议使用通配符导入
            if len(classes) >= 2:
                suggestions.append(f"{package}.*")
            else:
                # 否则建议具体导入
                for class_name in classes:
                    suggestions.append(f"{package}.{class_name}")

        return suggestions

    def _get_java_file_path(self):
        """从problems文件信息中获取Java文件路径"""
        if hasattr(self, 'java_file_info') and self.java_file_info.get('file'):
            file_path = self.java_file_info['file']
            # 清理路径：移除换行符、空格、file://前缀和$PROJECT_DIR$/前缀
            file_path = file_path.strip().replace('file://', '').replace('$PROJECT_DIR$/', '')
            return Path(file_path)
        return None

    def _scan_current_project_classes(self):
        """扫描当前工程中的所有类"""
        current_classes = {}
        src_dir = self.project_path / "src" / "main" / "java"

        if not src_dir.exists():
            return current_classes

        # 递归搜索所有.java文件
        for java_file in src_dir.rglob("*.java"):
            try:
                relative_path = java_file.relative_to(src_dir)
                package_parts = relative_path.parts[:-1]
                package_name = ".".join(package_parts)
                class_name = java_file.stem

                if package_name.startswith('com.cet'):
                    current_classes[class_name] = {
                        'full_class_name': f"{package_name}.{class_name}",
                        'package_name': package_name
                    }
            except:
                continue

        return current_classes

    def _analyze_wildcard_issues(self, wildcard_imports, current_classes):
        """分析通配符导入问题"""
        issues = []

        for wildcard_package in wildcard_imports:
            # 检查是否有类应该从当前工程导入而不是从这个通配符包
            misplaced_classes = []

            for class_name, class_info in current_classes.items():
                current_package = class_info['package_name']
                # 如果当前工程的类不在通配符导入的包中
                if not current_package.startswith(wildcard_package):
                    misplaced_classes.append({
                        'class_name': class_name,
                        'correct_package': current_package,
                        'wildcard_package': wildcard_package
                    })

            if misplaced_classes:
                issues.append({
                    'wildcard_package': wildcard_package,
                    'misplaced_classes': misplaced_classes
                })

        return issues

    def _generate_analysis_result(self, found_classes, wildcard_analysis=None, vector_search_results=None):
        """生成分析结果"""
        # 找出未找到的类
        found_class_names = set(found_classes.keys())
        all_class_names = set(cls['class_name'] for cls in self.missing_classes)
        not_found_classes = all_class_names - found_class_names

        # 生成JSON格式的报告（包含向量搜索结果）
        json_report = self._generate_json_report(found_classes, not_found_classes, wildcard_analysis, vector_search_results)

        return {
            'missing_classes': self.missing_classes,
            'found_classes': found_classes,
            'not_found_classes': list(not_found_classes),
            'wildcard_analysis': wildcard_analysis,
            'json_report': json_report
        }

    def _generate_json_report(self, found_classes, not_found_classes, wildcard_analysis=None, vector_search_results=None):
        """生成JSON格式的报告"""
        # 计算真正的问题数量：类名问题 + 通配符导入问题
        class_problems_count = len([mc for mc in self.missing_classes if mc.get('is_class', False)])
        wildcard_problems_count = len(wildcard_analysis) if wildcard_analysis else 0
        total_problems_count = class_problems_count + wildcard_problems_count

        # 确保java_file_info存在
        if not hasattr(self, 'java_file_info'):
            self.java_file_info = {'file': '', 'module': '', 'package': ''}

        report = {
            'file': self.java_file_info['file'],
            'module': self.java_file_info['module'],
            'package': self.java_file_info['package'],
            'problems_count': total_problems_count,
            'analysis_results': []
        }

        # 为每个缺失的类生成报告项（只包含真正的类名问题）
        for missing_class in self.missing_classes:
            if not missing_class['is_class'] or not missing_class['class_name']:
                continue

            class_name = missing_class['class_name']
            line_num = missing_class['line']
            lines_nums = missing_class.get('lines', [line_num])  # 获取所有行号
            description = missing_class['description']
            entry_point_type = missing_class.get('entry_point_type', 'file')  # 默认为'file'

            # 查找匹配的类路径
            matched_paths = []
            if class_name in found_classes:
                for result in found_classes[class_name]:
                    matched_paths.append(result['full_class_name'])

            # 查找向量搜索结果
            vector_suggest = []
            if vector_search_results and class_name in vector_search_results:
                vector_suggest = process_vector_search_results(vector_search_results[class_name])

            # 根据不同情况生成建议
            if matched_paths:
                if entry_point_type == 'file':
                    # file类型的entry_point，建议使用新的类路径替换
                    suggest = "请使用新的类路径替换"
                else:
                    # method或class类型的entry_point，建议添加新的import导入
                    suggest = "请添加新的import类导入"
            else:
                # 没有找到匹配的类路径，统一建议为"类已经废弃"
                suggest = "类已经废弃"

            analysis_item = {
                'class_name': class_name,
                'line': lines_nums,  # 现在line是一个数组
                'description': description,
                'matched_class_paths': matched_paths,
                'suggest': suggest
            }

            # 添加向量搜索结果
            if vector_suggest:
                analysis_item['vector_suggest'] = vector_suggest

            report['analysis_results'].append(analysis_item)

        # 添加通配符导入问题到analysis_results
        if wildcard_analysis:
            for issue in wildcard_analysis:
                wildcard_import = issue['wildcard_import']
                package_problems = issue.get('package_problems', [])

                # 提取无法解析的包符号
                problem_elements = [p['highlighted_element'] for p in package_problems]

                report['analysis_results'].append({
                    'package_path': f"{wildcard_import['package']}.*",
                    'line': wildcard_import['line'],
                    'description': '未解析的通配符导入',
                    'unresolved_package_symbols': problem_elements,
                    'suggest': '请使用正确的包路径替换'
                })

        return report



    def save_json_report(self, analysis_result, output_file=None):
        """保存JSON报告到文件"""
        if output_file is None:
            # 创建split_analysis目录
            problems_path = Path(self.problems_file)
            analysis_dir = problems_path.parent.parent / "split_analysis"
            analysis_dir.mkdir(exist_ok=True)

            # 基于problems文件名生成输出文件名
            output_file = analysis_dir / f"{problems_path.stem}_analysis_report.json"

        try:
            with open(output_file, 'w', encoding='utf-8') as f:
                json.dump(analysis_result['json_report'], f, ensure_ascii=False, indent=2)
            print(f"\nJSON报告已保存到: {output_file}")
            return str(output_file)
        except Exception as e:
            print(f"保存JSON报告失败: {e}")
            return None

    def _analyze_dependencies(self, found_classes):
        """分析依赖关系"""
        dependency_map = {}

        for class_name, results in found_classes.items():
            for result in results:
                jar_name = result['jar_name']

                # 从JAR名称推断Maven坐标
                maven_coords = self._infer_maven_coordinates(jar_name)

                if maven_coords:
                    if maven_coords not in dependency_map:
                        dependency_map[maven_coords] = []

                    dependency_map[maven_coords].append({
                        'class_name': class_name,
                        'full_class_name': result['full_class_name'],
                        'package_name': result['package_name'],
                        'jar_name': jar_name
                    })

        return dependency_map

    def _infer_maven_coordinates(self, jar_name):
        """从JAR名称推断Maven坐标"""
        # 移除版本号和.jar后缀
        base_name = jar_name.replace('.jar', '')

        # 常见的Maven坐标映射
        coordinate_patterns = {
            r'eem-auth-.*': 'com.cet.electric:eem-auth',
            r'eem-bll-common-.*': 'com.cet.electric:eem-bll-common',
            r'eem-common-.*': 'com.cet.electric:eem-common',
            r'piem-entity-.*': 'com.cet.electric:piem-entity',
            r'springfox-swagger2-.*': 'io.springfox:springfox-swagger2',
            r'spring-web-.*': 'org.springframework:spring-web',
            r'spring-webmvc-.*': 'org.springframework:spring-webmvc'
        }

        for pattern, coords in coordinate_patterns.items():
            if re.match(pattern, base_name):
                return coords

        # 如果没有匹配到，返回基于JAR名称的推测
        if '-' in base_name:
            parts = base_name.split('-')
            if len(parts) >= 2:
                # 简单推测：groupId可能是com.cet.electric，artifactId是前面的部分
                artifact_id = '-'.join(parts[:-1])  # 去掉最后一个部分（可能是版本）
                return f"unknown:{artifact_id}"

        return f"unknown:{base_name}"

    def _generate_fix_suggestions(self, found_classes, dependency_analysis):
        """生成修复建议"""
        suggestions = []

        # 按依赖分组生成建议
        for maven_coords, classes in dependency_analysis.items():
            group_id, artifact_id = maven_coords.split(':', 1)

            suggestion = {
                'type': 'dependency_found',
                'maven_coordinates': maven_coords,
                'group_id': group_id,
                'artifact_id': artifact_id,
                'classes': classes,
                'import_statements': [],
                'maven_dependency': self._generate_maven_dependency_xml(group_id, artifact_id)
            }

            # 生成import语句
            for cls_info in classes:
                suggestion['import_statements'].append(
                    f"import {cls_info['full_class_name']};"
                )

            suggestions.append(suggestion)

        return suggestions

    def _generate_maven_dependency_xml(self, group_id, artifact_id):
        """生成Maven依赖XML"""
        if group_id == 'com.cet.electric':
            return f'''<dependency>
                            <groupId>{group_id}</groupId>
                            <artifactId>{artifact_id}</artifactId>
                            <version>${{project.version}}</version>
                        </dependency>'''
        elif group_id in ['org.springframework', 'io.springfox']:
            return f'''<dependency>
                            <groupId>{group_id}</groupId>
                            <artifactId>{artifact_id}</artifactId>
                            <!-- 版本由父POM管理 -->
                        </dependency>'''
        else:
            return f'''<dependency>
                            <groupId>{group_id}</groupId>
                            <artifactId>{artifact_id}</artifactId>
                            <!-- 请确认正确的版本 -->
                        </dependency>'''

    def _search_similar_classes_by_vector(self, wildcard_analysis: Dict[str, Any]) -> Dict[str, List[Dict[str, Any]]]:
        """
        使用向量搜索查找相似的类
        Args:
            wildcard_analysis: 通配符分析结果（当前未使用，保留作为扩展接口）
        Returns:
            向量搜索结果
        """
        print("\n开始向量搜索相似类...")

        try:
            # 从 missing_classes 中提取类名列表（只包含真正的类名）
            class_names = [
                cls['class_name']
                for cls in self.missing_classes
                if cls.get('is_class', False) and cls.get('class_name')
            ]

            print(f"提取到 {len(class_names)} 个类名用于向量搜索: {class_names}")

            # 获取向量搜索服务并执行批量搜索
            vector_service = get_vector_search_service()
            vector_results = vector_service.batch_search_classes(class_names)

            print(f"向量搜索完成，共找到 {len(vector_results)} 个类的相似结果")
            return vector_results

        except Exception as e:
            print(f"向量搜索过程出错: {e}")
            return {}


def print_analysis_report(analysis):
    """打印分析报告（只显示com.cet开头的类）"""
    print(f"\n{'='*80}")
    print("精确类查找分析报告 (仅显示com.cet开头的类)")
    print(f"{'='*80}")

    # 基本统计
    total_missing = len(analysis['missing_classes'])
    total_found = len(analysis['found_classes'])
    total_not_found = len(analysis['not_found_classes'])

    print(f"\n📊 统计信息:")
    print(f"  缺失的类总数: {total_missing}")
    print(f"  找到的类数量 (com.cet): {total_found}")
    print(f"  未找到的类数量: {total_not_found}")

    # 找到的类详情（只显示com.cet开头的）
    if analysis['found_classes']:
        print(f"\n✅ 找到的类详情 (com.cet开头):")
        for class_name, results in analysis['found_classes'].items():
            print(f"\n📦 {class_name}:")

            # 分组显示：当前工程优先
            project_results = [r for r in results if r.get('source') == 'current_project']
            jar_results = [r for r in results if r.get('source') == 'jar_dependency']

            # 显示当前工程的结果
            if project_results:
                print(f"  🏠 当前工程:")
                for result in project_results:
                    print(f"    - 完整类名: {result['full_class_name']}")
                    print(f"    - 源文件: {result['jar_path']}")

            # 显示JAR包的结果
            if jar_results:
                print(f"  📚 JAR依赖:")
                for result in jar_results:
                    print(f"    - 完整类名: {result['full_class_name']}")
                    print(f"    - JAR包: {result['jar_name']}")

    # 未找到的类
    if analysis['not_found_classes']:
        print(f"\n❌ 未找到的类 (com.cet范围内):")
        for class_name in analysis['not_found_classes']:
            print(f"  - {class_name}")

    # 通配符导入问题
    if analysis.get('wildcard_analysis'):
        print(f"\n🔄 通配符导入问题:")
        for issue in analysis['wildcard_analysis']:
            wildcard_import = issue['wildcard_import']
            package_problems = issue.get('package_problems', [])

            print(f"  ❌ 第{wildcard_import['line']}行: {wildcard_import['package']}.* 包符号无法解析")
            if package_problems:
                problem_elements = [p['highlighted_element'] for p in package_problems]
                print(f"    无法解析的包符号: {', '.join(problem_elements)}")

    # JSON报告预览
    if 'json_report' in analysis:
        print(f"\n📋 JSON报告预览:")
        json_report = analysis['json_report']
        print(f"  文件: {json_report['file']}")
        print(f"  模块: {json_report['module']}")
        print(f"  包: {json_report['package']}")
        # 统计类名问题和通配符问题
        class_issues = [r for r in json_report['analysis_results'] if 'class_name' in r]
        wildcard_issues = [r for r in json_report['analysis_results'] if 'package_path' in r]
        print(f"  分析结果数量: {len(json_report['analysis_results'])}")
        print(f"  类名问题数量: {len(class_issues)}")
        print(f"  通配符问题数量: {len(wildcard_issues)}")

        for result in json_report['analysis_results']:
            if 'class_name' in result:
                # 类名问题
                status = "✓" if result['matched_class_paths'] else "✗"
                # 处理行号数组
                lines_info = result['line']
                if isinstance(lines_info, list):
                    if len(lines_info) > 1:
                        lines_str = f"第{', '.join(map(str, lines_info))}行"
                    else:
                        lines_str = f"第{lines_info[0]}行"
                else:
                    lines_str = f"第{lines_info}行"

                print(f"  {status} {result['class_name']} ({lines_str}) - {result['suggest']}")
                if result['matched_class_paths']:
                    for path in result['matched_class_paths']:
                        print(f"      → {path}")
            elif 'package_path' in result:
                # 通配符导入问题
                print(f"  ✗ {result['package_path']} (第{result['line']}行) - {result['suggest']}")
                if result.get('unresolved_package_symbols'):
                    symbols = ', '.join(result['unresolved_package_symbols'])
                    print(f"      无法解析的包符号: {symbols}")


def main():
    if len(sys.argv) < 3:
        print("使用方法: python precise_class_finder.py <problems_file> <project_path> [mvn_executable]")
        print("示例: python precise_class_finder.py problems.xml ./eem-solution-group-energy-core")
        print("      python precise_class_finder.py problems.xml ./eem-solution-group-energy-core D:\\maven\\bin\\mvn.cmd")
        return

    problems_file = sys.argv[1]
    project_path = sys.argv[2]
    mvn_executable = sys.argv[3] if len(sys.argv) > 3 else "mvn"

    # 检查文件是否存在
    if not os.path.exists(problems_file):
        print(f"错误: Problems文件 {problems_file} 不存在")
        return

    if not os.path.exists(project_path):
        print(f"错误: 项目路径 {project_path} 不存在")
        return

    finder = PreciseClassFinder(problems_file, project_path, mvn_executable)
    analysis = finder.analyze()

    # 打印控制台报告
    print_analysis_report(analysis)

    # 保存JSON报告
    json_file = finder.save_json_report(analysis)
    if json_file:
        print(f"✅ 分析完成，JSON报告已保存")
    else:
        print("❌ JSON报告保存失败")


class BatchAnalyzer:
    """批量分析器 - 处理split_problems文件夹中的所有XML文件"""

    def __init__(self, split_problems_dir="output/split_problems", project_path="../eem-solution-group-energy/"):
        """
        初始化批量分析器

        Args:
            split_problems_dir: split_problems目录路径
            project_path: 项目路径
        """
        self.split_problems_dir = Path(split_problems_dir)
        self.project_path = Path(project_path)
        self.analysis_dir = Path("output/split_analysis")

    def process_all_problems(self):
        """处理所有问题文件"""
        if not self.split_problems_dir.exists():
            print(f"错误: split_problems目录不存在: {self.split_problems_dir}")
            return []

        # 查找所有XML文件
        xml_files = list(self.split_problems_dir.glob("*_problems.xml"))
        if not xml_files:
            print(f"在{self.split_problems_dir}中未找到问题文件")
            return []

        print(f"找到 {len(xml_files)} 个问题文件需要处理")

        # 创建输出目录
        self.analysis_dir.mkdir(exist_ok=True)

        generated_files = []
        for xml_file in xml_files:
            try:
                print(f"\n处理文件: {xml_file.name}")

                # 创建精确类查找器
                finder = PreciseClassFinder(
                    problems_file=str(xml_file),
                    project_path=str(self.project_path)
                )

                # 执行分析
                analysis = finder.analyze()

                # 生成输出文件名 - 将_problems.xml替换为_problems_analysis_report.json
                output_filename = xml_file.stem.replace("_problems", "_problems_analysis_report") + ".json"
                output_file = self.analysis_dir / output_filename

                # 保存JSON报告
                json_file = finder.save_json_report(analysis, output_file)
                if json_file:
                    generated_files.append(json_file)
                    print(f"✅ 已生成: {output_filename}")
                else:
                    print(f"❌ 生成失败: {output_filename}")

            except Exception as e:
                print(f"处理文件 {xml_file.name} 时出错: {e}")

        print(f"\n批量处理完成，成功生成 {len(generated_files)} 个分析报告")
        return generated_files


if __name__ == '__main__':
    main()