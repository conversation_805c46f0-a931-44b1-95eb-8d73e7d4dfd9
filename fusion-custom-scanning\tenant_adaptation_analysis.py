import subprocess
import os
import sys
import time
import threading

def execute_commands():
    """
    按照用户要求执行命令：
    1. cd ../ (返回上一层级)
    2. <PERSON>wen (启动Qwen CLI工具)
    3. 输入test，返回输出结果
    """
    current_dir = os.getcwd()

    default_directory = "/eem-solution-group-energy/"
    print(">>> cd ../")
    print(">>> Qwen")
    input_text = f'中文回答 利用我的规则：@rules/04多租户适配.md 输出多租户的适配方案，需要进行多租户适配的文件夹是{default_directory}\n'
    print(f">>> {input_text}")
    print()

    try:
        # 启动进程，使用实时输出，指定编码
        process = subprocess.Popen(
            ['cmd', '/c', f'cd .. && qwen.cmd -y'],
            stdin=subprocess.PIPE,
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            text=True,
            cwd=current_dir,
            bufsize=1,  # 行缓冲
            universal_newlines=True,
            encoding='utf-8',
            errors='ignore'  # 忽略编码错误
        )

        # 发送输入
        process.stdin.write(input_text)
        process.stdin.flush()
        process.stdin.close()

        # 动态读取输出
        print("Qwen输出:")
        output_lines = []

        def read_output():
            try:
                while True:
                    line = process.stdout.readline()
                    if not line:
                        break
                    line = line.strip()
                    if line and 'Active code page' not in line and 'Microsoft Windows' not in line and line != '':
                        output_lines.append(line)
                        print(line)
                        sys.stdout.flush()
            except Exception as e:
                print(f"读取输出时出错: {e}")
                pass

        # 启动读取线程
        output_thread = threading.Thread(target=read_output)
        output_thread.daemon = True
        output_thread.start()

        # 等待进程完成，增加超时时间到120秒
        try:
            process.wait(timeout=5000)
        except subprocess.TimeoutExpired:
            print("\n执行超时，强制终止进程")
            process.kill()
            return False

        # 等待线程完成
        output_thread.join(timeout=5)

        # 读取错误输出
        stderr_output = process.stderr.read()
        if stderr_output and stderr_output.strip():
            print(f"\n错误信息: {stderr_output.strip()}")

        # 如果没有输出，尝试读取剩余内容
        if not output_lines:
            print("(等待Qwen响应...)")
            remaining_output = process.stdout.read()
            if remaining_output:
                lines = remaining_output.strip().split('\n')
                for line in lines:
                    line = line.strip()
                    if line and 'Active code page' not in line:
                        print(line)

        return process.returncode == 0

    except Exception as e:
        print(f"执行失败: {e}")
        return False

def main():
    success = execute_commands()

    if not success:
        print("\n注意: 请确保Qwen已正确安装并在PATH中")

if __name__ == "__main__":
    main()
