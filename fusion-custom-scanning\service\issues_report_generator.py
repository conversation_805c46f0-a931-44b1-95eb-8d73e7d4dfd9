#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
问题报告生成器
根据扫描结果生成Markdown格式的问题报告
使用类定义输出格式，直接字段映射
"""

import os
from collections import defaultdict
from typing import List, Dict, Any
from dataclasses import dataclass

@dataclass
class IssueReport:
    """问题报告类 - 定义输出格式"""
    error_type: str = ""
    error_code: str = ""
    missing_class: str = ""
    missing_method: str = ""
    missing_field: str = ""
    import_statements: List[str] = None
    calling_class: str = ""
    calling_method: str = ""
    old_dependency: str = ""
    current_dependency: str = ""
    usage_pattern: str = ""
    parameter_types: List[str] = None
    return_type: str = ""
    suggest: str = ""
    line: List[str] = None
    vector_suggest: List[str] = None  # 向量搜索建议

    def __post_init__(self):
        if self.import_statements is None:
            self.import_statements = []
        if self.parameter_types is None:
            self.parameter_types = []
        if self.line is None:
            self.line = []
        if self.vector_suggest is None:
            self.vector_suggest = []

class IssuesReportGenerator:
    """问题报告生成器"""
    
    def __init__(self):
        pass
    
    def extract_class_name(self, file_path: str) -> str:
        """从文件路径提取类名"""
        if file_path.endswith('.java'):
            return os.path.basename(file_path).replace('.java', '')
        else:
            return os.path.basename(file_path)
    
    def map_issue_to_report(self, issue: Dict[str, Any], class_name: str) -> IssueReport:
        """将扫描结果中的问题直接映射到 IssueReport 类"""
        report = IssueReport()

        # 直接字段映射
        report.error_type = issue.get('category', '')  # 问题类别
        report.error_code = issue.get('problemtype', '')  # error_code对应problemtype
        report.calling_class = class_name
        report.calling_method = issue.get('element_name', '')
        report.usage_pattern = issue.get('keyword', '')
        report.return_type = issue.get('return_type', '')

        # 处理导入问题的特殊字段 - 映射到现有字段
        if issue.get('category') == 'import_issues':
            details = issue.get('details', {})
            report.suggest = issue.get('suggestion', '')
            report.missing_class = details.get('class_name', '')
            # 将package信息映射到old_dependency字段
            report.old_dependency = details.get('package', '')
            # 将module信息映射到current_dependency字段
            report.current_dependency = details.get('module', '')
            # 提取向量搜索建议
            report.vector_suggest = issue.get('vector_suggest', [])
        else:
            report.suggest = issue.get('suggest', '')

        # 处理target_detection类型的特殊字段 - 映射到现有字段
        if issue.get('category') == 'target_detection':
            # 将property_type映射到missing_field字段
            report.missing_field = issue.get('property_type', '')
            # 将target_type映射到missing_method字段
            report.missing_method = issue.get('target_type', '')

        # 行号映射 - 特殊处理target_detection类型
        if issue.get('category') == 'target_detection' and 'lines' in issue:
            # 对于target_detection，使用所有相关行号（声明行 + 使用行）
            all_lines = issue.get('lines', [])
            declaration_line = issue.get('declaration_line', issue.get('line', 0))
            usage_lines = issue.get('usage_lines', [])

            if usage_lines:
                # 格式：声明行 + 使用行
                line_parts = [f"声明:{declaration_line}"]
                if len(usage_lines) == 1:
                    line_parts.append(f"使用:{usage_lines[0]}")
                else:
                    line_parts.append(f"使用:{','.join(map(str, usage_lines))}")
                report.line = [" | ".join(line_parts)]
            else:
                # 只有声明行
                report.line = [str(declaration_line)]
        else:
            # 常规行号处理
            line = issue.get('line', 0)
            end_line = issue.get('end_line', line)

            # 特殊处理注解：如果是注解且标记为精确行号，则只显示单行
            is_annotation_precise = issue.get('annotation_precise_line', False)

            if is_annotation_precise or line == end_line:
                report.line = [str(line)]
            else:
                report.line = [f"{line}-{end_line}"]

        return report
    
    def generate_markdown_report(self, import_issues_by_class: Dict[str, List[IssueReport]],
                               other_issues_by_class: Dict[str, List[IssueReport]],
                               total_issues: int) -> str:
        """生成Markdown格式的报告"""
        lines = []
        lines.append("# 代码扫描问题报告")
        lines.append("")
        lines.append(f"总问题数: {total_issues}")
        lines.append("")

        # 合并所有类的问题，每个类先显示导入问题，再显示其他问题
        all_classes = set(import_issues_by_class.keys()) | set(other_issues_by_class.keys())

        for class_name in sorted(all_classes):
            lines.append(f"## {class_name}")
            lines.append("")

            # 获取该类的导入问题和其他问题
            import_issues = import_issues_by_class.get(class_name, [])
            other_issues = other_issues_by_class.get(class_name, [])

            # 合并问题列表，导入问题在前
            all_issues = import_issues + other_issues

            # 输出所有问题，保持连续编号
            for i, issue_report in enumerate(all_issues, 1):
                lines.append(f"### 问题 {i}")
                self._append_issue_fields(lines, issue_report)
                lines.append("")

        return "\n".join(lines)

    def _append_issue_fields(self, lines: List[str], issue_report: IssueReport):
        """添加问题字段到输出行"""
        # 输出 IssueReport 类的所有非空字段
        if issue_report.error_type:
            lines.append(f"error_type: \"{issue_report.error_type}\"")
        if issue_report.error_code:
            lines.append(f"error_code: \"{issue_report.error_code}\"")
        if issue_report.missing_class:
            lines.append(f"missing_class: \"{issue_report.missing_class}\"")
        if issue_report.missing_method:
            lines.append(f"missing_method: \"{issue_report.missing_method}\"")
        if issue_report.missing_field:
            lines.append(f"missing_field: \"{issue_report.missing_field}\"")
        if issue_report.import_statements:
            lines.append("import_statements:")
            for imp in issue_report.import_statements:
                lines.append(f"\t- \"{imp}\"")
        if issue_report.calling_class:
            lines.append(f"calling_class: \"{issue_report.calling_class}\"")
        if issue_report.calling_method:
            lines.append(f"calling_method: \"{issue_report.calling_method}\"")
        if issue_report.old_dependency:
            lines.append(f"old_dependency: \"{issue_report.old_dependency}\"")
        if issue_report.current_dependency:
            lines.append(f"current_dependency: \"{issue_report.current_dependency}\"")
        if issue_report.usage_pattern:
            lines.append(f"usage_pattern: \"{issue_report.usage_pattern}\"")
        if issue_report.parameter_types:
            param_str = '", "'.join(issue_report.parameter_types)
            lines.append(f"parameter_types: [\"{param_str}\"]")
        if issue_report.return_type:
            lines.append(f"return_type: \"{issue_report.return_type}\"")
        if issue_report.suggest:
            lines.append(f"suggest: \"{issue_report.suggest}\"")
        if issue_report.vector_suggest:
            vector_str = '", "'.join(issue_report.vector_suggest)
            lines.append(f"vector_suggest: [\"{vector_str}\"]")
        if issue_report.line:
            line_str = '", "'.join(issue_report.line)
            lines.append(f"line: [\"{line_str}\"]")
    
    def generate_report(self, scan_data: Dict[str, Any], output_file: str = "output/issues_report.md") -> bool:
        """生成问题报告"""
        try:
            # 分别获取导入问题和其他问题
            import_issues = scan_data.get('summary', {}).get('import_issues', [])
            global_issues = scan_data.get('summary', {}).get('global_issues', [])
            structure_issues = scan_data.get('summary', {}).get('structure_issues', [])

            # 按类分组导入问题
            import_issues_by_class = defaultdict(list)
            for issue in import_issues:
                file_path = issue.get('file', '')
                class_name = self.extract_class_name(file_path)
                issue_report = self.map_issue_to_report(issue, class_name)
                import_issues_by_class[class_name].append(issue_report)

            # 按类分组其他问题
            other_issues_by_class = defaultdict(list)
            for issue in global_issues + structure_issues:
                file_path = issue.get('file', '')
                class_name = self.extract_class_name(file_path)
                issue_report = self.map_issue_to_report(issue, class_name)
                other_issues_by_class[class_name].append(issue_report)

            # 生成Markdown报告
            total_issues = len(import_issues) + len(global_issues) + len(structure_issues)
            report = self.generate_markdown_report(import_issues_by_class, other_issues_by_class, total_issues)

            # 保存报告
            with open(output_file, 'w', encoding='utf-8') as f:
                f.write(report)

            print(f"✅ 问题报告已生成: {output_file}")
            return True

        except Exception as e:
            print(f"❌ 生成问题报告时出错: {e}")
            import traceback
            traceback.print_exc()
            return False
