import subprocess
import os
import time

def execute_exact_command_sequence():
    """
    精确按照用户要求执行命令序列：
    1. cd ../ (返回上一层级)
    2. <PERSON>wen (启动Qwen CLI工具)
    3. 输入test，返回输出结果
    """
    print("=== 精确执行用户要求的命令序列 ===")
    print("1. cd ../")
    print("2. Qwen")
    print("3. 输入: test")
    print("=" * 50)
    
    current_dir = os.getcwd()
    parent_dir = os.path.dirname(current_dir)
    
    print(f"当前工作目录: {current_dir}")
    print(f"目标目录 (../): {parent_dir}")
    
    # 步骤1: 执行cd ../命令
    print(f"\n>>> 步骤1: cd ../")
    try:
        # 使用正确的cmd语法
        result = subprocess.run(
            ['cmd', '/c', 'cd .. && echo 当前目录: && cd'],
            capture_output=True,
            text=True,
            cwd=current_dir,  # 从当前目录开始
            timeout=5
        )
        
        if result.returncode == 0:
            print("✅ cd ../执行成功")
            output_lines = result.stdout.strip().split('\n')
            for line in output_lines:
                if line.strip() and 'Active code page' not in line:
                    print(f"   {line}")
        else:
            print(f"❌ cd ../失败: {result.stderr}")
            
    except Exception as e:
        print(f"❌ cd ../异常: {e}")
    
    # 步骤2和3: 在上级目录执行Qwen并发送test
    print(f"\n>>> 步骤2: 启动Qwen")
    print(f">>> 步骤3: 发送输入 'test'")
    
    # 首先确认Qwen命令的正确路径
    qwen_command = None
    possible_commands = [
        'qwen',
        'Qwen', 
        'qwen.cmd',
        'Qwen.cmd'
    ]
    
    print("查找Qwen命令...")
    for cmd in possible_commands:
        try:
            result = subprocess.run(['where', cmd], capture_output=True, text=True)
            if result.returncode == 0:
                qwen_command = cmd
                qwen_path = result.stdout.strip().split('\n')[0]
                print(f"✅ 找到Qwen: {cmd} -> {qwen_path}")
                break
        except:
            continue
    
    if not qwen_command:
        print("❌ 未找到Qwen命令")
        return None
    
    # 在上级目录执行Qwen
    try:
        print(f"在目录 {parent_dir} 中启动 {qwen_command}...")
        
        process = subprocess.Popen(
            [qwen_command],
            stdin=subprocess.PIPE,
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            text=True,
            cwd=parent_dir,  # 关键：在上级目录执行
            bufsize=0
        )
        
        print("向Qwen发送输入: test")
        
        # 发送输入并等待结果
        try:
            stdout, stderr = process.communicate(input='test\n', timeout=15)
            
            print(f"\nQwen执行结果:")
            print(f"返回码: {process.returncode}")
            
            if stdout:
                print("✅ 标准输出:")
                print("-" * 30)
                print(stdout)
                print("-" * 30)
            
            if stderr:
                print("⚠️ 错误输出:")
                print("-" * 30)
                print(stderr)
                print("-" * 30)
            
            return {
                'success': True,
                'returncode': process.returncode,
                'stdout': stdout,
                'stderr': stderr
            }
            
        except subprocess.TimeoutExpired:
            print("❌ Qwen执行超时")
            process.kill()
            return {'success': False, 'error': 'timeout'}
            
    except Exception as e:
        print(f"❌ Qwen执行异常: {e}")
        return {'success': False, 'error': str(e)}

def verify_command_sequence():
    """
    验证命令序列的每个步骤
    """
    print(f"\n" + "=" * 50)
    print("=== 验证命令序列 ===")
    
    current_dir = os.getcwd()
    parent_dir = os.path.dirname(current_dir)
    
    # 验证步骤1: cd ../
    print("验证步骤1: cd ../")
    if os.path.exists(parent_dir) and os.path.isdir(parent_dir):
        print(f"✅ 上级目录存在: {parent_dir}")
        try:
            os.chdir(parent_dir)
            actual_dir = os.getcwd()
            os.chdir(current_dir)  # 切换回来
            print(f"✅ 可以切换到上级目录: {actual_dir}")
        except Exception as e:
            print(f"❌ 无法切换到上级目录: {e}")
    else:
        print(f"❌ 上级目录不存在或不可访问")
    
    # 验证步骤2: Qwen命令
    print(f"\n验证步骤2: Qwen命令")
    try:
        result = subprocess.run(['where', 'qwen'], capture_output=True, text=True)
        if result.returncode == 0:
            paths = result.stdout.strip().split('\n')
            print(f"✅ Qwen命令可用:")
            for path in paths:
                print(f"   {path}")
        else:
            print("❌ Qwen命令不在PATH中")
    except Exception as e:
        print(f"❌ 检查Qwen命令失败: {e}")
    
    # 验证步骤3: 输入处理
    print(f"\n验证步骤3: 输入处理能力")
    print("✅ Python subprocess支持stdin输入")
    print("✅ 可以发送'test\\n'到进程")

def main():
    print("最终版本：精确按照用户要求执行控制台命令")
    print("命令序列：")
    print("  1. cd ../")
    print("  2. Qwen")
    print("  3. 输入test")
    print("=" * 60)
    
    # 验证所有步骤
    verify_command_sequence()
    
    # 执行完整序列
    result = execute_exact_command_sequence()
    
    print(f"\n" + "=" * 60)
    print("=== 执行总结 ===")
    
    if result and result.get('success'):
        print("🎉 成功完成所有步骤！")
        print("✅ cd ../: 成功")
        print("✅ Qwen启动: 成功")
        print("✅ 输入test: 成功")
        print("✅ 获得输出: 成功")
    else:
        print("⚠️ 执行过程中遇到问题")
        if result:
            print(f"错误信息: {result.get('error', '未知错误')}")
    
    print("\n注意事项:")
    print("- 确保Qwen已正确安装并在PATH中")
    print("- 某些Qwen版本可能需要特定的启动参数")
    print("- 如果Qwen是交互式工具，可能需要调整超时时间")

if __name__ == "__main__":
    main()
