import subprocess
import os

def execute_commands():
    """
    按照用户要求执行命令：
    1. cd ../ (返回上一层级)
    2. <PERSON>wen (启动Qwen CLI工具)
    3. 输入test，返回输出结果
    """
    current_dir = os.getcwd()

    print(">>> cd ../")
    print(">>> <PERSON>wen")
    print(">>> @rules/04多租户适配.md 班组能耗插件中的classesscheme需要进行多租户适配，请根据相关规则生成班组能耗插件的多租户适配方案")
    print()

    try:
        # 执行完整命令序列
        result = subprocess.run(
            ['cmd', '/c', 'cd .. && qwen.cmd'],
            input='@rules/04多租户适配.md 班组能耗插件中的classesscheme需要进行多租户适配，请根据相关规则生成班组能耗插件的多租户适配方案\n',
            capture_output=True,
            text=True,
            cwd=current_dir,
            timeout=15
        )

        # 输出结果
        if result.stdout:
            # 清理输出，移除不必要的信息
            output = result.stdout.strip()
            lines = output.split('\n')

            # 过滤掉系统信息行
            filtered_lines = []
            for line in lines:
                line = line.strip()
                if line and 'Active code page' not in line and 'Microsoft Windows' not in line:
                    filtered_lines.append(line)

            if filtered_lines:
                print("Qwen输出:")
                for line in filtered_lines:
                    print(line)
            else:
                print("Qwen执行完成，无输出内容")

        if result.stderr:
            print(f"\n错误信息: {result.stderr.strip()}")

        return result.returncode == 0

    except subprocess.TimeoutExpired:
        print("执行超时")
        return False
    except Exception as e:
        print(f"执行失败: {e}")
        return False

def main():
    success = execute_commands()

    if not success:
        print("\n注意: 请确保Qwen已正确安装并在PATH中")

if __name__ == "__main__":
    main()
