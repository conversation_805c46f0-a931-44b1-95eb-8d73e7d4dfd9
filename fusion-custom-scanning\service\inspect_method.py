import subprocess
import os
import re
from collections import defaultdict
import argparse
import time
import json

class InspectRunner:
    def __init__(self, project_path=None, inspection_profile=None, output_path=None, format="json"):
        self.project_path = project_path or r"E:\ai-x\code\energy-solution-fusion\eem-solution-group-energy"
        self.inspection_profile = inspection_profile or r"E:\ai-x\code\energy-solution-fusion\inspect\ai_method.xml"
        self.output_path = output_path or "."
        self.format = format
        self.xml_file = os.path.join(self.output_path, "JavaAnnotator.xml")
        
    def pre_check(self):
        """执行预检查，验证路径和环境"""
        print("开始预检查...")
        
        # 检查项目路径
        if not os.path.exists(self.project_path):
            print(f"错误: 项目路径不存在: {self.project_path}")
            return False
        print(f"✓ 项目路径存在: {self.project_path}")
        
        # 检查检查配置文件
        if not os.path.exists(self.inspection_profile):
            print(f"错误: 检查配置文件不存在: {self.inspection_profile}")
            return False
        print(f"✓ 检查配置文件存在: {self.inspection_profile}")
        
        # 创建输出目录
        try:
            os.makedirs(self.output_path, exist_ok=True)
            print(f"✓ 输出目录已准备: {self.output_path}")
        except Exception as e:
            print(f"错误: 无法创建输出目录 {self.output_path}: {e}")
            return False
        
        # 检查 idea64.exe 是否可用
        try:
            result = subprocess.run(
                ["where", "idea64.exe"], 
                capture_output=True, 
                text=True, 
                shell=True
            )
            if result.returncode == 0:
                idea_path = result.stdout.strip()
                print(f"✓ 找到 idea64.exe: {idea_path}")
            else:
                print("警告: 在 PATH 中未找到 idea64.exe，请确保 IntelliJ IDEA 已正确安装并添加到 PATH")
                print("请检查以下路径是否存在 idea64.exe:")
                common_paths = [
                    r"C:\Program Files\JetBrains\IntelliJ IDEA 2023.3\bin\idea64.exe",
                    r"C:\Program Files\JetBrains\IntelliJ IDEA 2024.1\bin\idea64.exe",
                    r"C:\Program Files\JetBrains\IntelliJ IDEA Community Edition\bin\idea64.exe"
                ]
                for path in common_paths:
                    if os.path.exists(path):
                        print(f"  发现: {path}")
                    else:
                        print(f"  不存在: {path}")
                return False
        except Exception as e:
            print(f"检查 idea64.exe 时出错: {e}")
            return False
        
        return True
    
    def diagnose_failure(self, result):
        """诊断命令执行失败的原因"""
        print("\n开始故障诊断...")
        
        # 检查是否是 IDEA 实例已运行的问题
        if result.stdout and "一次只能运行一个 IDEA 实例" in result.stdout:
            print("✗ 发现问题: IntelliJ IDEA 已有实例在运行")
            print("解决方案:")
            print("1. 关闭所有打开的 IntelliJ IDEA 窗口")
            print("2. 检查任务管理器中是否有 idea64.exe 进程在后台运行")
            print("3. 如果有，结束这些进程后再重试")
            print("4. 或者等待一段时间后再试")
            
            # 尝试自动检查和结束 IDEA 进程
            self.try_close_idea_processes()
            return
        
        # 检查常见的错误码
        error_codes = {
            1: "一般错误 - 可能是参数错误或环境问题",
            2: "文件未找到 - 检查路径是否正确",
            3: "路径未找到 - 检查目录是否存在",
            9009: "程序未找到 - idea64.exe 不在 PATH 中"
        }
        
        if result.returncode in error_codes:
            print(f"错误码 {result.returncode}: {error_codes[result.returncode]}")
        
        # 分析错误输出
        if result.stderr:
            stderr_lower = result.stderr.lower()
            if "not found" in stderr_lower or "找不到" in stderr_lower:
                print("可能的原因: idea64.exe 未找到")
                print("解决方案: 确保 IntelliJ IDEA 已安装并添加到 PATH 环境变量")
            elif "access denied" in stderr_lower or "拒绝访问" in stderr_lower:
                print("可能的原因: 权限不足")
                print("解决方案: 以管理员权限运行")
            elif "invalid" in stderr_lower or "无效" in stderr_lower:
                print("可能的原因: 参数无效")
                print("解决方案: 检查路径格式和参数")
        
        # 检查是否有 ja-netfilter 相关的输出
        if result.stdout and "ja-netfilter" in result.stdout:
            print("检测到 ja-netfilter，这可能会影响 IDEA 的正常运行")
            print("建议: 使用原版 IntelliJ IDEA 进行代码检查")
        
        # 提供额外的诊断建议
        print("\n诊断建议:")
        print("1. 确保 IntelliJ IDEA 已正确安装")
        print("2. 检查环境变量 PATH 中是否包含 IntelliJ IDEA 的 bin 目录")
        print("3. 尝试使用完整路径运行 idea64.exe")
        print("4. 检查项目路径和配置文件路径是否正确")
        print("5. 确保有足够的权限访问相关目录")
    
    def try_close_idea_processes(self):
        """尝试检查和关闭 IDEA 进程"""
        try:
            print("\n检查正在运行的 IDEA 进程...")
            
            # 使用 tasklist 检查进程
            result = subprocess.run(
                ["tasklist", "/FI", "IMAGENAME eq idea64.exe"],
                capture_output=True,
                text=True,
                encoding='gbk'  # Windows 中文系统使用 GBK 编码
            )
            
            if "idea64.exe" in result.stdout:
                print("发现正在运行的 IDEA 进程:")
                print(result.stdout)
                
                print("\n提示: 请手动关闭所有 IntelliJ IDEA 窗口，然后再重试")
                print("或者使用以下命令强制结束进程:")
                print("taskkill /F /IM idea64.exe")
                
                # 可以提供一个选项让用户选择是否自动结束
                user_input = input("\n是否要自动结束 IDEA 进程? (y/N): ").strip().lower()
                if user_input == 'y':
                    print("正在结束 IDEA 进程...")
                    kill_result = subprocess.run(
                        ["taskkill", "/F", "/IM", "idea64.exe"],
                        capture_output=True,
                        text=True,
                        encoding='gbk'
                    )
                    if kill_result.returncode == 0:
                        print("✓ IDEA 进程已结束")
                        print("请稍等几秒后再重试...")
                    else:
                        print(f"结束进程失败: {kill_result.stderr}")
                else:
                    print("请手动关闭 IDEA 后再重试")
            else:
                print("未发现正在运行的 IDEA 进程")
                
        except Exception as e:
            print(f"检查 IDEA 进程时出错: {e}")
        
    def run_inspect(self):
        """执行inspect.bat命令"""
        # 先进行预检查
        if not self.pre_check():
            return False
            
        # 尝试多种方式运行 inspect 命令
        return self.try_run_inspect_command()
    
    def try_run_inspect_command(self):
        """尝试多种方式运行 inspect 命令"""
        commands_to_try = [
            # 方式1: 直接使用 idea64.exe
            ["idea64.exe", "inspect", self.project_path, self.inspection_profile, self.output_path, "-v2"],
            
            # 方式2: 使用完整的 idea64.exe 路径 (常见安装位置)
            *self.get_idea_full_path_commands()
        ]
        
        for i, cmd in enumerate(commands_to_try, 1):
            print(f"\n尝试方式 {i}: {' '.join(cmd)}")
            
            try:
                result = subprocess.run(
                    cmd, 
                    capture_output=True, 
                    text=True, 
                    shell=True,
                    encoding='utf-8',
                    errors='ignore'
                )
                
                print(f"返回码: {result.returncode}")
                
                # 显示详细的输出信息
                if result.stdout:
                    print("标准输出:")
                    print(result.stdout[:500] + "..." if len(result.stdout) > 500 else result.stdout)
                
                if result.stderr:
                    print("错误输出:")
                    print(result.stderr[:500] + "..." if len(result.stderr) > 500 else result.stderr)
                
                if result.returncode == 0:
                    print(f"方式 {i} 执行成功!")
                    self.wait_for_file_generation()
                    return True
                else:
                    print(f"方式 {i} 执行失败，返回码: {result.returncode}")
                    if i == 1:  # 只在第一种方式失败时显示详细诊断
                        self.diagnose_failure(result)
                    
            except Exception as e:
                print(f"方式 {i} 执行时出错: {e}")
                continue
        
        print("\n所有方式都失败了，请检查 IntelliJ IDEA 安装")
        return False
    
    def get_idea_full_path_commands(self):
        """获取可能的 IntelliJ IDEA 完整路径命令"""
        idea_paths = [
            r"C:\Program Files\JetBrains\IntelliJ IDEA 2024.2\bin\idea64.exe",
            r"C:\Program Files\JetBrains\IntelliJ IDEA 2024.1\bin\idea64.exe",
            r"C:\Program Files\JetBrains\IntelliJ IDEA 2023.3\bin\idea64.exe",
            r"C:\Program Files\JetBrains\IntelliJ IDEA 2023.2\bin\idea64.exe",
            r"C:\Program Files\JetBrains\IntelliJ IDEA Community Edition 2024.2\bin\idea64.exe",
            r"C:\Program Files\JetBrains\IntelliJ IDEA Community Edition 2024.1\bin\idea64.exe",
            r"C:\Program Files\JetBrains\IntelliJ IDEA Community Edition 2023.3\bin\idea64.exe"
        ]
        
        commands = []
        for idea_path in idea_paths:
            if os.path.exists(idea_path):
                commands.append([idea_path, "inspect", self.project_path, self.inspection_profile, self.output_path, "-v2"])
        
        return commands
    
    def wait_for_file_generation(self, timeout=60):
        """等待XML文件生成"""
        print(f"等待 {self.xml_file} 文件生成...")
        start_time = time.time()
        
        while time.time() - start_time < timeout:
            if os.path.exists(self.xml_file):
                initial_size = os.path.getsize(self.xml_file)
                time.sleep(2)
                if os.path.exists(self.xml_file):
                    current_size = os.path.getsize(self.xml_file)
                    if current_size == initial_size and current_size > 0:
                        print(f"文件已生成，大小: {current_size} 字节")
                        return True
            time.sleep(1)
        
        print(f"等待文件生成超时 ({timeout}秒)")
        return False
    
    def parse_problems_manually(self):
        """手动解析problem块，不依赖XML解析器"""
        if not os.path.exists(self.xml_file):
            print(f"XML文件 {self.xml_file} 不存在")
            return []
        
        try:
            print("开始手动解析problem块...")
            
            with open(self.xml_file, 'r', encoding='utf-8', errors='ignore') as f:
                content = f.read()
            
            # 使用正则表达式提取所有problem块
            problem_pattern = r'<problem>(.*?)</problem>'
            problem_matches = re.findall(problem_pattern, content, re.DOTALL)
            
            print(f"找到 {len(problem_matches)} 个problem块")
            
            problems = []
            for i, problem_content in enumerate(problem_matches):
                try:
                    problem_data = self.parse_single_problem(problem_content)
                    if problem_data:
                        problems.append(problem_data)
                except Exception as e:
                    print(f"解析第 {i+1} 个problem时出错: {e}")
                    continue
            
            print(f"成功解析 {len(problems)} 个problem")
            return problems
            
        except Exception as e:
            print(f"手动解析problems时出错: {e}")
            return []
    
    def parse_single_problem(self, problem_content):
        """解析单个problem块的内容"""
        problem_data = {}
        
        # 定义需要提取的字段及其正则表达式
        field_patterns = {
            'file': r'<file>(.*?)</file>',
            'line': r'<line>(\d+)</line>',
            'module': r'<module>(.*?)</module>',
            'package': r'<package>(.*?)</package>',
            'description': r'<description>(.*?)</description>',
            'highlighted_element': r'<highlighted_element>(.*?)</highlighted_element>',
            'entry_point_fqname': r'<entry_point[^>]*FQNAME="([^"]*)"'
        }
        
        for field, pattern in field_patterns.items():
            match = re.search(pattern, problem_content, re.DOTALL)
            if match:
                problem_data[field] = match.group(1).strip()
        
        return problem_data if problem_data else None
    
    def extract_class_name_from_data(self, problem_data):
        """从problem数据中提取类名"""
        # 尝试从entry_point FQNAME提取
        if 'entry_point_fqname' in problem_data:
            fqname = problem_data['entry_point_fqname']
            if fqname:
                # 处理不同格式的FQNAME
                parts = fqname.split()
                if len(parts) > 1:
                    class_part = parts[0]
                    return class_part.split('.')[-1]
                elif '.' in fqname:
                    return fqname.split('.')[-1]
        
        # 尝试从file路径提取
        if 'file' in problem_data:
            file_path = problem_data['file']
            if file_path:
                filename = os.path.basename(file_path)
                if filename.endswith('.java'):
                    return filename[:-5]
        
        return 'Unknown'
    
    def filter_method_problems(self, problems):
        """筛选出方法相关的问题"""
        method_problems = []
        
        for problem in problems:
            description = problem.get('description', '')
            if '无法解析方法' in description or 'Cannot resolve method' in description:
                method_problems.append(problem)
        
        return method_problems
    
    def parse_and_generate_report(self):
        """解析问题并生成报告"""
        print("开始解析并生成报告...")
        
        # 手动解析所有problems
        all_problems = self.parse_problems_manually()
        if not all_problems:
            print("未能解析到任何问题")
            return False
        
        print(f"总共解析到 {len(all_problems)} 个问题")
        
        # 筛选方法相关问题
        method_problems = self.filter_method_problems(all_problems)
        print(f"找到 {len(method_problems)} 个方法解析问题")
        
        if not method_problems:
            print("未找到方法解析相关问题，显示问题类型统计...")
            self.show_problem_types(all_problems)
            return False
        
        # 按类名分组
        grouped_problems = defaultdict(list)
        for problem in method_problems:
            class_name = self.extract_class_name_from_data(problem)
            grouped_problems[class_name].append(problem)
        
        # 生成报告
        if self.format.lower() == "json":
            self.generate_json_report_from_data(grouped_problems, len(method_problems))
        else:
            self.generate_markdown_report_from_data(grouped_problems, len(method_problems))
        return True
    
    def show_problem_types(self, problems):
        """显示问题类型统计"""
        problem_types = {}
        
        for problem in problems[:200]:  # 只看前200个避免输出太多
            description = problem.get('description', 'Unknown')
            problem_types[description] = problem_types.get(description, 0) + 1
        
        print("问题类型统计 (前15种):")
        for desc, count in sorted(problem_types.items(), key=lambda x: x[1], reverse=True)[:15]:
            print(f"  {count}: {desc}")
        
        # 特别检查是否有其他方法相关的描述
        method_related = []
        for desc in problem_types.keys():
            if any(keyword in desc for keyword in ['方法', '函数', 'method', 'function', '调用', 'call']):
                method_related.append((desc, problem_types[desc]))
        
        if method_related:
            print("\n可能的方法相关问题:")
            for desc, count in method_related:
                print(f"  {count}: {desc}")
    
    def generate_markdown_report_from_data(self, grouped_problems, total_count):
        """从数据生成Markdown报告"""
        report_file = os.path.join(self.output_path, "method_issues_report.md")
        
        try:
            with open(report_file, 'w', encoding='utf-8') as f:
                f.write("# 方法扫描问题报告\n\n")
                f.write(f"总问题数: {total_count}\n\n")
                
                for class_name, problems in grouped_problems.items():
                    f.write(f"## {class_name}\n\n")
                    
                    for idx, problem in enumerate(problems, 1):
                        module = problem.get('module', 'Unknown')
                        package = problem.get('package', 'Unknown')
                        description = problem.get('description', 'Unknown')
                        line = problem.get('line', 'Unknown')
                        highlighted_element = problem.get('highlighted_element', 'Unknown')
                        
                        # 构造方法名
                        if highlighted_element != 'Unknown':
                            missing_method = f"{highlighted_element}()"
                        else:
                            # 尝试从描述中提取方法名
                            method_match = re.search(r"'([^']+)'", description)
                            if method_match:
                                missing_method = f"{method_match.group(1)}()"
                            else:
                                missing_method = 'Unknown'
                        
                        f.write(f"### 问题 {idx}\n")
                        f.write(f'error_code: "method_issues"\n')
                        f.write(f'module: "{module}"\n')
                        f.write(f'package: "{package}"\n')
                        f.write(f'class: "{class_name}"\n')
                        f.write(f'missing_method: "{missing_method}"\n')
                        f.write(f'description: "{description}"\n')
                        f.write(f'line: [{line}]\n\n')
            
            print(f"Markdown报告已生成: {report_file}")
            return True
            
        except Exception as e:
            print(f"生成Markdown报告时出错: {e}")
            return False
    
    def generate_json_report_from_data(self, grouped_problems, total_count):
        """从数据生成JSON报告"""
        report_file = os.path.join(self.output_path, "method_issues_report.json")
        
        try:
            # 准备报告数据
            report_data = {
                "title": "方法扫描问题报告",
                "total_count": total_count,
                "issues_by_class": {}
            }
            
            for class_name, problems in grouped_problems.items():
                report_data["issues_by_class"][class_name] = []
                for idx, problem in enumerate(problems, 1):
                    module = problem.get('module', 'Unknown')
                    package = problem.get('package', 'Unknown')
                    description = problem.get('description', 'Unknown')
                    line = problem.get('line', 'Unknown')
                    highlighted_element = problem.get('highlighted_element', 'Unknown')
                    
                    # 构造方法名
                    if highlighted_element != 'Unknown':
                        missing_method = f"{highlighted_element}()"
                    else:
                        # 尝试从描述中提取方法名
                        method_match = re.search(r"'([^']+)'", description)
                        if method_match:
                            missing_method = f"{method_match.group(1)}()"
                        else:
                            missing_method = 'Unknown'
                    
                    issue_data = {
                        "issue_id": idx,
                        "error_code": "method_issues",
                        "module": module,
                        "package": package,
                        "class": class_name,
                        "missing_method": missing_method,
                        "description": description,
                        "line": line
                    }
                    
                    report_data["issues_by_class"][class_name].append(issue_data)
            
            # 写入JSON文件
            with open(report_file, 'w', encoding='utf-8') as f:
                json.dump(report_data, f, ensure_ascii=False, indent=2)
            
            print(f"JSON报告已生成: {report_file}")
            return True
            
        except Exception as e:
            print(f"生成JSON报告时出错: {e}")
            return False
    
    def run(self):
        """运行完整流程"""
        print("开始执行inspect.bat...")
        if self.run_inspect():
            print("开始解析并生成报告...")
            if self.parse_and_generate_report():
                print("流程执行完成!")
            else:
                print("解析或报告生成失败")
        else:
            print("inspect.bat执行失败")

def main():
    parser = argparse.ArgumentParser(description='运行inspect.bat并生成方法问题报告')
    parser.add_argument('--project-path', help='项目根目录路径')
    parser.add_argument('--inspection-profile', help='检查配置文件路径')  
    parser.add_argument('--output-path', help='输出目录路径')
    parser.add_argument('--format', choices=['json', 'md'], default='json', help='报告输出格式: json 或 md (默认: json)')
    
    args = parser.parse_args()
    
    runner = InspectRunner(
        project_path=args.project_path,
        inspection_profile=args.inspection_profile,
        output_path=args.output_path,
        format=args.format
    )
    
    runner.run()

if __name__ == "__main__":
    main()