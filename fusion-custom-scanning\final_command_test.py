import subprocess
import os
import time

def execute_commands_in_sequence():
    """
    真正按照用户要求的顺序执行命令：
    1. cd ../ (返回上一层级)
    2. <PERSON>wen (启动Qwen CLI工具)
    3. 输入test，返回输出结果
    """
    print("=== 按照要求的顺序执行控制台命令 ===")
    print("命令序列:")
    print("1. cd ../")
    print("2. Qwen")
    print("3. 输入: test")
    print("-" * 50)
    
    # 首先验证cd ../命令
    print("\n步骤1: 执行 cd ../")
    try:
        result = subprocess.run(
            ['cmd', '/c', 'cd .. && echo 切换到目录: && cd'],
            capture_output=True,
            text=True,
            timeout=5
        )
        print("✅ cd ../命令执行成功")
        print(f"目标目录: {result.stdout.strip().split()[-1]}")
    except Exception as e:
        print(f"❌ cd ../命令失败: {e}")
        return
    
    # 获取上级目录路径
    current_dir = os.getcwd()
    parent_dir = os.path.dirname(current_dir)
    print(f"当前目录: {current_dir}")
    print(f"上级目录: {parent_dir}")
    
    # 步骤2和3: 在上级目录执行Qwen命令并发送test输入
    print(f"\n步骤2: 在上级目录执行 Qwen 命令")
    print(f"步骤3: 向Qwen发送输入 'test'")
    
    try:
        # 启动Qwen进程，工作目录设置为上级目录
        print("启动Qwen进程...")
        process = subprocess.Popen(
            ['Qwen'],
            stdin=subprocess.PIPE,
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            text=True,
            cwd=parent_dir  # 在上级目录执行
        )
        
        # 发送test输入
        print("发送输入: test")
        stdout, stderr = process.communicate(input='test\n', timeout=15)
        
        if process.returncode == 0:
            print("✅ Qwen命令执行成功")
            print("Qwen输出结果:")
            print("-" * 30)
            print(stdout)
            print("-" * 30)
        else:
            print(f"❌ Qwen执行失败 (返回码: {process.returncode})")
            print(f"错误信息: {stderr}")
            
    except FileNotFoundError:
        print("❌ 找不到Qwen命令")
        print("请确保:")
        print("  - Qwen已正确安装")
        print("  - Qwen在系统PATH中")
        print("  - 或者Qwen可执行文件在上级目录中")
        
        # 尝试查找Qwen
        print(f"\n在上级目录查找Qwen相关文件:")
        try:
            result = subprocess.run(
                ['cmd', '/c', 'dir *qwen* /s /b 2>nul || echo 未找到Qwen相关文件'],
                capture_output=True,
                text=True,
                cwd=parent_dir,
                timeout=5
            )
            print(result.stdout)
        except:
            pass
            
    except subprocess.TimeoutExpired:
        print("❌ Qwen执行超时")
        process.kill()
    except Exception as e:
        print(f"❌ 执行过程中发生错误: {e}")

def simulate_manual_execution():
    """
    模拟手动在控制台执行命令的过程
    """
    print("\n" + "=" * 60)
    print("=== 模拟手动控制台操作 ===")
    
    current_dir = os.getcwd()
    parent_dir = os.path.dirname(current_dir)
    
    print("模拟用户在控制台中的操作:")
    print(f"C:\\> cd {current_dir}")
    print(f"{current_dir}> cd ../")
    print(f"{parent_dir}> Qwen")
    print("Qwen> test")
    print("Qwen> (等待输出...)")
    
    print(f"\n实际执行结果:")
    print(f"✅ 当前目录: {current_dir}")
    print(f"✅ 上级目录: {parent_dir}")
    
    # 检查上级目录是否存在
    if os.path.exists(parent_dir):
        print(f"✅ 上级目录存在且可访问")
        
        # 列出上级目录的内容
        try:
            items = os.listdir(parent_dir)
            print(f"✅ 上级目录包含 {len(items)} 个项目")
            print("   主要内容:")
            for item in items[:5]:  # 只显示前5个
                item_path = os.path.join(parent_dir, item)
                item_type = "目录" if os.path.isdir(item_path) else "文件"
                print(f"   - {item} ({item_type})")
            if len(items) > 5:
                print(f"   ... 还有 {len(items) - 5} 个项目")
        except Exception as e:
            print(f"❌ 无法读取上级目录内容: {e}")
    else:
        print(f"❌ 上级目录不存在")
    
    # 检查Qwen命令
    print(f"\n检查Qwen命令可用性:")
    try:
        result = subprocess.run(['where', 'Qwen'], capture_output=True, text=True)
        if result.returncode == 0:
            print(f"✅ 找到Qwen: {result.stdout.strip()}")
        else:
            print("❌ 系统PATH中未找到Qwen命令")
    except:
        print("❌ 无法检查Qwen命令")

def main():
    print("Python控制台命令顺序执行测试")
    print("目标: 真正按照控制台命令顺序执行")
    print("1. cd ../")
    print("2. Qwen")
    print("3. 输入test")
    
    # 执行命令序列
    execute_commands_in_sequence()
    
    # 模拟手动执行过程
    simulate_manual_execution()
    
    print("\n" + "=" * 60)
    print("=== 测试完成 ===")
    print("如果Qwen命令不可用，请:")
    print("1. 安装Qwen并确保在PATH中")
    print("2. 或者将Qwen可执行文件放在上级目录")
    print("3. 或者修改脚本中的命令路径")

if __name__ == "__main__":
    main()
