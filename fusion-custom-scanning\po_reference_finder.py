#!/usr/bin/env python3
"""
PO Reference Finder - Java项目中PO类引用查找工具

该工具用于分析Java项目中PO(Persistent Object)类的引用关系和调用链.
使用AST语法树分析技术,能够准确识别类的使用位置,并向上追踪调用链直到根节点.
"""

from abc import ABC, abstractmethod
from dataclasses import dataclass
from enum import Enum
from typing import List, Dict, Any, Optional
import argparse
import sys
import os
import javalang
import logging


class ReferenceType(Enum):
    """引用类型枚举"""
    IMPORT = "import"
    VARIABLE = "variable"
    PARAMETER = "parameter"
    RETURN_TYPE = "return_type"


@dataclass
class Parameter:
    """方法参数信息"""
    name: str
    type_name: str
    line_number: int


@dataclass
class Reference:
    """引用信息数据模型"""
    file_path: str
    package_name: str
    class_name: str
    method_name: str
    line_numbers: List[int]
    reference_type: ReferenceType
    parameters: List[str]
    context_code: str


@dataclass
class MethodInfo:
    """方法信息数据模型"""
    name: str
    class_name: str
    package_name: str
    parameters: List[Parameter]
    return_type: str
    line_number: int
    file_path: str


@dataclass
class MethodCall:
    """方法调用信息"""
    caller_method: MethodInfo
    called_method: MethodInfo
    line_number: int


@dataclass
class CallChain:
    """调用链数据模型"""
    root_method: MethodInfo
    chain_path: List[MethodCall]
    target_reference: Reference
    depth: int
    is_circular: bool = False  # 是否包含循环调用
    is_root: bool = False      # 是否到达真正的根节点


@dataclass
class CallerInfo:
    """调用者信息"""
    method_info: MethodInfo
    call_line: int
    call_context: str


@dataclass
class ImportInfo:
    """导入信息"""
    import_path: str
    class_name: str
    line_number: int
    is_static: bool = False


@dataclass
class ClassInfo:
    """类信息"""
    name: str
    package_name: str
    file_path: str
    methods: List[MethodInfo]
    imports: List[ImportInfo]


@dataclass
class CallGraph:
    """调用图"""
    methods: Dict[str, MethodInfo]
    call_relationships: Dict[str, List[CallerInfo]]


@dataclass
class AnalysisResult:
    """分析结果数据模型"""
    target_class: str
    total_references: int
    affected_files: int
    references: List[Reference]
    call_chains: List[CallChain]
    statistics: Dict[str, Any]


class JavaASTParser(ABC):
    """Java AST解析器抽象基类"""
    
    @abstractmethod
    def parse_file(self, file_path: str) -> Any:
        """解析Java文件生成AST"""
        pass
    
    @abstractmethod
    def extract_imports(self, ast: Any) -> List[ImportInfo]:
        """从AST中提取导入信息"""
        pass
    
    @abstractmethod
    def extract_class_info(self, ast: Any) -> ClassInfo:
        """从AST中提取类信息"""
        pass
    
    @abstractmethod
    def extract_methods(self, ast: Any) -> List[MethodInfo]:
        """从AST中提取方法信息"""
        pass


class JavaLangASTParser(JavaASTParser):
    """基于javalang的Java AST解析器实现"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
    
    def parse_file(self, file_path: str) -> Optional[javalang.tree.CompilationUnit]:
        """解析Java文件生成AST
        
        Args:
            file_path: Java文件路径
            
        Returns:
            解析后的AST对象,解析失败时返回None
        """
        try:
            # 检查文件是否存在
            if not os.path.exists(file_path):
                self.logger.error(f"文件不存在: {file_path}")
                return None
            
            # 尝试多种编码格式读取文件
            content = self._read_file_with_encoding_detection(file_path)
            if content is None:
                return None
            
            # 使用javalang解析Java代码
            ast = javalang.parse.parse(content)
            return ast
            
        except javalang.parser.JavaSyntaxError as e:
            self.logger.error(f"Java语法错误: {file_path}, {e}")
            return None
        except Exception as e:
            self.logger.error(f"解析文件失败: {file_path}, {e}")
            return None
    
    def extract_imports(self, ast: javalang.tree.CompilationUnit) -> List[ImportInfo]:
        """从AST中提取导入信息
        
        Args:
            ast: Java AST对象
            
        Returns:
            导入信息列表
        """
        imports = []
        if not ast or not ast.imports:
            return imports
        
        for imp in ast.imports:
            # 提取类名(最后一个部分)
            class_name = imp.path.split('.')[-1] if imp.path else ""
            
            import_info = ImportInfo(
                import_path=imp.path,
                class_name=class_name,
                line_number=imp.position.line if hasattr(imp, 'position') and imp.position else 0,
                is_static=imp.static if hasattr(imp, 'static') else False
            )
            imports.append(import_info)
        
        return imports
    
    def extract_class_info(self, ast: javalang.tree.CompilationUnit) -> Optional[ClassInfo]:
        """从AST中提取类信息
        
        Args:
            ast: Java AST对象
            
        Returns:
            类信息对象,未找到类时返回None
        """
        if not ast:
            return None
        
        # 获取包名
        package_name = ast.package.name if ast.package else ""
        
        # 查找类声明
        for path, node in ast.filter(javalang.tree.ClassDeclaration):
            class_name = node.name
            
            # 提取方法信息
            methods = self.extract_methods(ast)
            
            # 提取导入信息
            imports = self.extract_imports(ast)
            
            return ClassInfo(
                name=class_name,
                package_name=package_name,
                file_path="",  # 将在调用时设置
                methods=methods,
                imports=imports
            )
        
        return None
    
    def extract_methods(self, ast: javalang.tree.CompilationUnit) -> List[MethodInfo]:
        """从AST中提取方法信息
        
        Args:
            ast: Java AST对象
            
        Returns:
            方法信息列表
        """
        methods = []
        if not ast:
            return methods
        
        # 获取包名和类名
        package_name = ast.package.name if ast.package else ""
        class_name = ""
        
        # 查找类声明以获取类名
        for path, node in ast.filter(javalang.tree.ClassDeclaration):
            class_name = node.name
            break
        
        # 提取所有方法声明
        for path, node in ast.filter(javalang.tree.MethodDeclaration):
            # 提取方法参数
            parameters = []
            if node.parameters:
                for param in node.parameters:
                    param_type = self._get_type_name(param.type)
                    parameters.append(Parameter(
                        name=param.name,
                        type_name=param_type,
                        line_number=param.position.line if hasattr(param, 'position') and param.position else 0
                    ))
            
            # 提取返回类型
            return_type = self._get_type_name(node.return_type) if node.return_type else "void"
            
            method_info = MethodInfo(
                name=node.name,
                class_name=class_name,
                package_name=package_name,
                parameters=parameters,
                return_type=return_type,
                line_number=node.position.line if hasattr(node, 'position') and node.position else 0,
                file_path=""  # 将在调用时设置
            )
            methods.append(method_info)
        
        return methods
    
    def _get_type_name(self, type_node) -> str:
        """从类型节点中提取类型名称
        
        Args:
            type_node: javalang类型节点
            
        Returns:
            类型名称字符串
        """
        if not type_node:
            return ""
        
        if hasattr(type_node, 'name'):
            return type_node.name
        elif hasattr(type_node, 'type'):
            # 处理泛型类型
            base_type = self._get_type_name(type_node.type)
            if hasattr(type_node, 'arguments') and type_node.arguments:
                args = [self._get_type_name(arg.type) for arg in type_node.arguments if hasattr(arg, 'type')]
                if args:
                    return f"{base_type}<{', '.join(args)}>"
            return base_type
        elif isinstance(type_node, str):
            return type_node
        else:
            return str(type_node)
    
    def _read_file_with_encoding_detection(self, file_path: str) -> Optional[str]:
        """使用编码检测读取文件内容
        
        Args:
            file_path: 文件路径
            
        Returns:
            文件内容字符串,读取失败时返回None
        """
        # 常见的编码格式,按优先级排序
        encodings = [
            'utf-8',        # 最常用的编码
            'utf-8-sig',    # 带BOM的UTF-8
            'gbk',          # 中文Windows系统常用
            'gb2312',       # 简体中文
            'gb18030',      # 中文国标
            'iso-8859-1',   # Latin-1,兼容性好
            'cp1252',       # Windows-1252
            'ascii'         # 基本ASCII
        ]
        
        for encoding in encodings:
            try:
                with open(file_path, 'r', encoding=encoding) as f:
                    content = f.read()
                
                # 验证读取的内容是否合理(包含基本的Java关键字)
                if self._validate_java_content(content):
                    self.logger.debug(f"成功使用编码 {encoding} 读取文件: {file_path}")
                    return content
                else:
                    self.logger.debug(f"编码 {encoding} 读取的内容不像Java文件: {file_path}")
                    
            except UnicodeDecodeError:
                self.logger.debug(f"编码 {encoding} 无法读取文件: {file_path}")
                continue
            except Exception as e:
                self.logger.debug(f"使用编码 {encoding} 读取文件时发生错误: {file_path}, {e}")
                continue
        
        self.logger.error(f"无法使用任何编码读取文件: {file_path}")
        return None
    
    def _validate_java_content(self, content: str) -> bool:
        """验证内容是否像Java源代码
        
        Args:
            content: 文件内容
            
        Returns:
            是否像Java源代码
        """
        if not content or len(content.strip()) == 0:
            return False
        
        # 检查是否包含Java关键字或结构
        java_indicators = [
            'package ', 'import ', 'class ', 'interface ', 'enum ',
            'public ', 'private ', 'protected ', 'static ', 'final ',
            'void ', 'return ', 'if ', 'for ', 'while ', 'try ', 'catch ',
            '{', '}', ';'
        ]
        
        content_lower = content.lower()
        indicator_count = sum(1 for indicator in java_indicators if indicator in content_lower)
        
        # 如果包含足够多的Java指示符,认为是Java文件
        return indicator_count >= 3


class ReferenceAnalyzer(ABC):
    """引用分析器抽象基类"""
    
    @abstractmethod
    def find_references(self, ast: Any, target_class: str) -> List[Reference]:
        """查找目标类的所有引用"""
        pass
    
    @abstractmethod
    def analyze_import_references(self, ast: Any, target_class: str) -> List[Reference]:
        """分析导入引用"""
        pass
    
    @abstractmethod
    def analyze_variable_declarations(self, ast: Any, target_class: str) -> List[Reference]:
        """分析变量声明引用"""
        pass
    
    @abstractmethod
    def analyze_method_parameters(self, ast: Any, target_class: str) -> List[Reference]:
        """分析方法参数引用"""
        pass
    
    @abstractmethod
    def analyze_return_types(self, ast: Any, target_class: str) -> List[Reference]:
        """分析返回类型引用"""
        pass


class JavaLangReferenceAnalyzer(ReferenceAnalyzer):
    """基于javalang的引用分析器实现"""
    
    def __init__(self, ast_parser: JavaASTParser):
        self.ast_parser = ast_parser
        self.logger = logging.getLogger(__name__)
    
    def find_references(self, ast: javalang.tree.CompilationUnit, target_class: str, file_path: str = "") -> List[Reference]:
        """查找目标类的所有引用
        
        Args:
            ast: Java AST对象
            target_class: 目标PO类名
            file_path: 文件路径
            
        Returns:
            引用信息列表
        """
        if not ast:
            return []
        
        references = []
        
        # 分析各种类型的引用
        references.extend(self.analyze_import_references(ast, target_class, file_path))
        references.extend(self.analyze_variable_declarations(ast, target_class, file_path))
        references.extend(self.analyze_method_parameters(ast, target_class, file_path))
        references.extend(self.analyze_return_types(ast, target_class, file_path))
        
        return references
    
    def analyze_import_references(self, ast: javalang.tree.CompilationUnit, target_class: str, file_path: str = "") -> List[Reference]:
        """分析导入引用
        
        Args:
            ast: Java AST对象
            target_class: 目标PO类名
            file_path: 文件路径
            
        Returns:
            导入引用列表
        """
        references = []
        if not ast or not ast.imports:
            return references
        
        # 获取包名和类名
        package_name = ast.package.name if ast.package else ""
        class_name = self._get_class_name(ast)
        
        for imp in ast.imports:
            if not imp.path:
                continue
            
            # 检查是否导入了目标类
            imported_class = imp.path.split('.')[-1]
            if imported_class == target_class or imp.path.endswith(f".{target_class}"):
                reference = Reference(
                    file_path=file_path,
                    package_name=package_name,
                    class_name=class_name,
                    method_name="",  # import语句不在方法内
                    line_numbers=[imp.position.line if hasattr(imp, 'position') and imp.position else 0],
                    reference_type=ReferenceType.IMPORT,
                    parameters=[],
                    context_code=f"import {imp.path};"
                )
                references.append(reference)
        
        return references
    
    def extract_class_info(self, ast: javalang.tree.CompilationUnit) -> Optional[ClassInfo]:
        """从AST中提取类信息
        
        Args:
            ast: Java AST对象
            
        Returns:
            类信息对象,未找到类时返回None
        """
        if not ast:
            return None
        
        # 获取包名
        package_name = ast.package.name if ast.package else ""
        
        # 查找类声明
        for path, node in ast.filter(javalang.tree.ClassDeclaration):
            class_name = node.name
            
            # 提取方法信息
            methods = self.extract_methods(ast)
            
            # 提取导入信息
            imports = self.extract_imports(ast)
            
            return ClassInfo(
                name=class_name,
                package_name=package_name,
                file_path="",  # 将在调用时设置
                methods=methods,
                imports=imports
            )
        
        return None
    
    def extract_methods(self, ast: javalang.tree.CompilationUnit) -> List[MethodInfo]:
        """从AST中提取方法信息
        
        Args:
            ast: Java AST对象
            
        Returns:
            方法信息列表
        """
        methods = []
        if not ast:
            return methods
        
        # 获取包名和类名
        package_name = ast.package.name if ast.package else ""
        class_name = ""
        
        # 查找类声明以获取类名
        for path, node in ast.filter(javalang.tree.ClassDeclaration):
            class_name = node.name
            break
        
        # 提取所有方法声明
        for path, node in ast.filter(javalang.tree.MethodDeclaration):
            # 提取方法参数
            parameters = []
            if node.parameters:
                for param in node.parameters:
                    param_type = self._get_type_name(param.type)
                    parameters.append(Parameter(
                        name=param.name,
                        type_name=param_type,
                        line_number=param.position.line if hasattr(param, 'position') and param.position else 0
                    ))
            
            # 提取返回类型
            return_type = self._get_type_name(node.return_type) if node.return_type else "void"
            
            method_info = MethodInfo(
                name=node.name,
                class_name=class_name,
                package_name=package_name,
                parameters=parameters,
                return_type=return_type,
                line_number=node.position.line if hasattr(node, 'position') and node.position else 0,
                file_path=""  # 将在调用时设置
            )
            methods.append(method_info)
        
        return methods
    
    def _get_type_name(self, type_node) -> str:
        """从类型节点中提取类型名称
        
        Args:
            type_node: javalang类型节点
            
        Returns:
            类型名称字符串
        """
        if not type_node:
            return ""
        
        if hasattr(type_node, 'name'):
            return type_node.name
        elif hasattr(type_node, 'type'):
            # 处理泛型类型
            base_type = self._get_type_name(type_node.type)
            if hasattr(type_node, 'arguments') and type_node.arguments:
                args = [self._get_type_name(arg.type) for arg in type_node.arguments if hasattr(arg, 'type')]
                if args:
                    return f"{base_type}<{', '.join(args)}>"
            return base_type
        elif isinstance(type_node, str):
            return type_node
        else:
            return str(type_node)
    
    def _read_file_with_encoding_detection(self, file_path: str) -> Optional[str]:
        """使用编码检测读取文件内容
        
        Args:
            file_path: 文件路径
            
        Returns:
            文件内容字符串,读取失败时返回None
        """
        # 常见的编码格式,按优先级排序
        encodings = [
            'utf-8',        # 最常用的编码
            'utf-8-sig',    # 带BOM的UTF-8
            'gbk',          # 中文Windows系统常用
            'gb2312',       # 简体中文
            'gb18030',      # 中文国标
            'iso-8859-1',   # Latin-1,兼容性好
            'cp1252',       # Windows-1252
            'ascii'         # 基本ASCII
        ]
        
        for encoding in encodings:
            try:
                with open(file_path, 'r', encoding=encoding) as f:
                    content = f.read()
                
                # 验证读取的内容是否合理(包含基本的Java关键字)
                if self._validate_java_content(content):
                    self.logger.debug(f"成功使用编码 {encoding} 读取文件: {file_path}")
                    return content
                else:
                    self.logger.debug(f"编码 {encoding} 读取的内容不像Java文件: {file_path}")
                    
            except UnicodeDecodeError:
                self.logger.debug(f"编码 {encoding} 无法读取文件: {file_path}")
                continue
            except Exception as e:
                self.logger.debug(f"使用编码 {encoding} 读取文件时发生错误: {file_path}, {e}")
                continue
        
        self.logger.error(f"无法使用任何编码读取文件: {file_path}")
        return None
    
    def _validate_java_content(self, content: str) -> bool:
        """验证内容是否像Java源代码
        
        Args:
            content: 文件内容
            
        Returns:
            是否像Java源代码
        """
        if not content or len(content.strip()) == 0:
            return False
        
        # 检查是否包含Java关键字或结构
        java_indicators = [
            'package ', 'import ', 'class ', 'interface ', 'enum ',
            'public ', 'private ', 'protected ', 'static ', 'final ',
            'void ', 'return ', 'if ', 'for ', 'while ', 'try ', 'catch ',
            '{', '}', ';'
        ]
        
        content_lower = content.lower()
        indicator_count = sum(1 for indicator in java_indicators if indicator in content_lower)
        
        # 如果包含足够多的Java指示符,认为是Java文件
        return indicator_count >= 3


class ReferenceAnalyzer(ABC):
    """引用分析器抽象基类"""
    
    @abstractmethod
    def find_references(self, ast: Any, target_class: str) -> List[Reference]:
        """查找目标类的所有引用"""
        pass
    
    @abstractmethod
    def analyze_import_references(self, ast: Any, target_class: str) -> List[Reference]:
        """分析导入引用"""
        pass
    
    @abstractmethod
    def analyze_variable_declarations(self, ast: Any, target_class: str) -> List[Reference]:
        """分析变量声明引用"""
        pass
    
    @abstractmethod
    def analyze_method_parameters(self, ast: Any, target_class: str) -> List[Reference]:
        """分析方法参数引用"""
        pass
    
    @abstractmethod
    def analyze_return_types(self, ast: Any, target_class: str) -> List[Reference]:
        """分析返回类型引用"""
        pass


class JavaLangReferenceAnalyzer(ReferenceAnalyzer):
    """基于javalang的引用分析器实现"""
    
    def __init__(self, ast_parser: JavaASTParser):
        self.ast_parser = ast_parser
        self.logger = logging.getLogger(__name__)
    
    def find_references(self, ast: javalang.tree.CompilationUnit, target_class: str, file_path: str = "") -> List[Reference]:
        """查找目标类的所有引用
        
        Args:
            ast: Java AST对象
            target_class: 目标PO类名
            file_path: 文件路径
            
        Returns:
            引用信息列表
        """
        if not ast:
            return []
        
        references = []
        
        # 分析各种类型的引用
        references.extend(self.analyze_import_references(ast, target_class, file_path))
        references.extend(self.analyze_variable_declarations(ast, target_class, file_path))
        references.extend(self.analyze_method_parameters(ast, target_class, file_path))
        references.extend(self.analyze_return_types(ast, target_class, file_path))
        
        return references
    
    def analyze_import_references(self, ast: javalang.tree.CompilationUnit, target_class: str, file_path: str = "") -> List[Reference]:
        """分析导入引用
        
        Args:
            ast: Java AST对象
            target_class: 目标PO类名
            file_path: 文件路径
            
        Returns:
            导入引用列表
        """
        references = []
        if not ast or not ast.imports:
            return references
        
        # 获取包名和类名
        package_name = ast.package.name if ast.package else ""
        class_name = self._get_class_name(ast)
        
        for imp in ast.imports:
            if not imp.path:
                continue
            
            # 检查是否导入了目标类
            imported_class = imp.path.split('.')[-1]
            if imported_class == target_class or imp.path.endswith(f".{target_class}"):
                reference = Reference(
                    file_path=file_path,
                    package_name=package_name,
                    class_name=class_name,
                    method_name="",  # import语句不在方法内
                    line_numbers=[imp.position.line if hasattr(imp, 'position') and imp.position else 0],
                    reference_type=ReferenceType.IMPORT,
                    parameters=[],
                    context_code=f"import {imp.path};"
                )
                references.append(reference)
        
        return references
    
    def analyze_variable_declarations(self, ast: javalang.tree.CompilationUnit, target_class: str, file_path: str = "") -> List[Reference]:
        """分析变量声明引用,包括字段声明和局部变量声明
        
        Args:
            ast: Java AST对象
            target_class: 目标PO类名
            file_path: 文件路径
            
        Returns:
            变量声明引用列表
        """
        references = []
        if not ast:
            return references
        
        # 获取包名和类名
        package_name = ast.package.name if ast.package else ""
        class_name = self._get_class_name(ast)
        
        # 查找所有变量声明(包括字段声明和局部变量声明)
        for path, node in ast.filter(javalang.tree.VariableDeclaration):
            if not node.type:
                continue
            
            # 获取变量类型名称
            type_name = self._extract_type_name(node.type)
            
            # 检查是否使用了目标类(包括泛型中的使用)
            if self._is_target_class_reference(type_name, target_class):
                # 查找包含此变量声明的方法
                method_name = self._find_containing_method(ast, node)
                
                # 获取变量声明的行号
                line_number = node.position.line if hasattr(node, 'position') and node.position else 0
                
                # 构建上下文代码
                context_code = self._build_variable_context(node, type_name)
                
                reference = Reference(
                    file_path=file_path,
                    package_name=package_name,
                    class_name=class_name,
                    method_name=method_name,
                    line_numbers=[line_number],
                    reference_type=ReferenceType.VARIABLE,
                    parameters=[],
                    context_code=context_code
                )
                references.append(reference)
        
        # 查找字段声明(FieldDeclaration)
        for path, node in ast.filter(javalang.tree.FieldDeclaration):
            if not node.type:
                continue
            
            # 获取字段类型名称
            type_name = self._extract_type_name(node.type)
            
            # 检查是否使用了目标类(包括泛型中的使用)
            if self._is_target_class_reference(type_name, target_class):
                # 字段声明不在方法内
                method_name = ""
                
                # 获取字段声明的行号
                line_number = node.position.line if hasattr(node, 'position') and node.position else 0
                
                # 构建上下文代码
                context_code = self._build_field_context(node, type_name)
                
                reference = Reference(
                    file_path=file_path,
                    package_name=package_name,
                    class_name=class_name,
                    method_name=method_name,
                    line_numbers=[line_number],
                    reference_type=ReferenceType.VARIABLE,
                    parameters=[],
                    context_code=context_code
                )
                references.append(reference)
        
        return references
    
    def analyze_method_parameters(self, ast: javalang.tree.CompilationUnit, target_class: str, file_path: str = "") -> List[Reference]:
        """分析方法参数引用,支持复杂泛型类型
        
        Args:
            ast: Java AST对象
            target_class: 目标PO类名
            file_path: 文件路径
            
        Returns:
            方法参数引用列表
        """
        references = []
        if not ast:
            return references
        
        # 获取包名和类名
        package_name = ast.package.name if ast.package else ""
        class_name = self._get_class_name(ast)
        
        # 查找所有方法声明(包括构造函数)
        method_nodes = []
        for path, node in ast.filter(javalang.tree.MethodDeclaration):
            method_nodes.append(('method', node))
        for path, node in ast.filter(javalang.tree.ConstructorDeclaration):
            method_nodes.append(('constructor', node))
        
        for method_type, node in method_nodes:
            if not node.parameters:
                continue
            
            method_name = node.name if method_type == 'method' else f"{class_name}()"
            line_numbers = []
            parameters = []
            target_param_details = []
            
            # 检查每个参数
            for param in node.parameters:
                if not param.type:
                    continue
                
                param_type = self._extract_type_name(param.type)
                
                # 检查是否使用了目标类(包括泛型中的使用)
                if self._is_target_class_reference(param_type, target_class):
                    line_number = param.position.line if hasattr(param, 'position') and param.position else 0
                    line_numbers.append(line_number)
                    
                    # 记录具体的参数信息
                    param_detail = {
                        'name': param.name,
                        'type': param_type,
                        'line': line_number,
                        'usage_context': self._analyze_parameter_usage_context(param_type, target_class)
                    }
                    target_param_details.append(param_detail)
                    parameters.append(f"{param_type} {param.name}")
            
            # 如果找到了目标类的参数引用
            if line_numbers:
                # 构建完整的方法签名
                all_params = []
                for param in node.parameters:
                    param_type = self._extract_type_name(param.type) if param.type else "unknown"
                    all_params.append(f"{param_type} {param.name}")
                
                # 获取返回类型(如果是方法)
                return_type = ""
                if method_type == 'method' and hasattr(node, 'return_type') and node.return_type:
                    return_type = self._extract_type_name(node.return_type)
                
                context_code = self._build_method_signature_context(
                    method_name, all_params, return_type, method_type
                )
                
                reference = Reference(
                    file_path=file_path,
                    package_name=package_name,
                    class_name=class_name,
                    method_name=method_name,
                    line_numbers=line_numbers,
                    reference_type=ReferenceType.PARAMETER,
                    parameters=parameters,
                    context_code=context_code
                )
                references.append(reference)
        
        return references
    
    def analyze_return_types(self, ast: javalang.tree.CompilationUnit, target_class: str, file_path: str = "") -> List[Reference]:
        """分析返回类型引用,支持复杂泛型类型
        
        Args:
            ast: Java AST对象
            target_class: 目标PO类名
            file_path: 文件路径
            
        Returns:
            返回类型引用列表
        """
        references = []
        if not ast:
            return references
        
        # 获取包名和类名
        package_name = ast.package.name if ast.package else ""
        class_name = self._get_class_name(ast)
        
        # 查找所有方法声明
        for path, node in ast.filter(javalang.tree.MethodDeclaration):
            if not node.return_type:
                continue
            
            return_type = self._extract_type_name(node.return_type)
            
            # 检查是否使用了目标类(包括泛型中的使用)
            if self._is_target_class_reference(return_type, target_class):
                method_name = node.name
                line_number = node.position.line if hasattr(node, 'position') and node.position else 0
                
                # 分析返回类型中目标类的使用上下文
                usage_context = self._analyze_return_type_usage_context(return_type, target_class)
                
                # 构建完整的方法签名
                params = []
                if node.parameters:
                    for param in node.parameters:
                        param_type = self._extract_type_name(param.type) if param.type else "unknown"
                        params.append(f"{param_type} {param.name}")
                
                context_code = self._build_method_signature_context(
                    method_name, params, return_type, 'method'
                )
                
                reference = Reference(
                    file_path=file_path,
                    package_name=package_name,
                    class_name=class_name,
                    method_name=method_name,
                    line_numbers=[line_number],
                    reference_type=ReferenceType.RETURN_TYPE,
                    parameters=[f"returns: {usage_context}"],
                    context_code=context_code
                )
                references.append(reference)
        
        return references
    
    def _get_class_name(self, ast: javalang.tree.CompilationUnit) -> str:
        """从AST中获取类名"""
        for path, node in ast.filter(javalang.tree.ClassDeclaration):
            return node.name
        return ""
    
    def _extract_type_name(self, type_node) -> str:
        """从类型节点中提取类型名称,支持泛型和复杂类型"""
        if not type_node:
            return ""
        
        # 处理ReferenceType(包含泛型参数的类型)
        if isinstance(type_node, javalang.tree.ReferenceType):
            if hasattr(type_node, 'arguments') and type_node.arguments:
                # 这是一个参数化类型(泛型)
                base_type = type_node.name
                args = []
                for arg in type_node.arguments:
                    if hasattr(arg, 'type'):
                        args.append(self._extract_type_name(arg.type))
                    elif hasattr(arg, 'name'):
                        args.append(arg.name)
                    else:
                        # 处理通配符类型 ? extends/super
                        arg_str = self._extract_wildcard_type(arg)
                        if arg_str:
                            args.append(arg_str)
                
                if args:
                    return f"{base_type}<{', '.join(args)}>"
                else:
                    return base_type
            else:
                # 简单的引用类型
                return type_node.name
        
        # 处理基本类型引用
        elif hasattr(type_node, 'name'):
            return type_node.name
        
        # 处理数组类型
        elif hasattr(type_node, 'type'):
            base_type = self._extract_type_name(type_node.type)
            # 检查是否是数组类型
            if hasattr(type_node, 'dimensions'):
                dimensions = getattr(type_node, 'dimensions', 0)
                return base_type + '[]' * dimensions
            return base_type
        
        # 处理字符串类型
        elif isinstance(type_node, str):
            return type_node
        
        # 其他情况
        else:
            return str(type_node)
    
    def _extract_wildcard_type(self, wildcard_node) -> str:
        """提取通配符类型信息,如 ? extends UserPO"""
        if not wildcard_node:
            return ""
        
        # 处理 ? extends Type
        if hasattr(wildcard_node, 'extends') and wildcard_node.extends:
            extends_type = self._extract_type_name(wildcard_node.extends)
            return f"? extends {extends_type}"
        
        # 处理 ? super Type
        elif hasattr(wildcard_node, 'super') and wildcard_node.super:
            super_type = self._extract_type_name(wildcard_node.super)
            return f"? super {super_type}"
        
        # 处理单独的 ?
        else:
            return "?"
    
    def _is_target_class_reference(self, type_name: str, target_class: str) -> bool:
        """检查类型名称是否引用了目标类,支持复杂泛型类型"""
        if not type_name or not target_class:
            return False
        
        # 直接匹配
        if type_name == target_class:
            return True
        
        # 数组类型匹配,例如 UserPO[], UserPO[][]
        if '[]' in type_name:
            base_type = type_name.replace('[]', '')
            if base_type == target_class:
                return True
        
        # 泛型中的匹配,例如 List<UserPO>, Map<String, UserPO>
        if '<' in type_name and '>' in type_name:
            return self._check_generic_type_reference(type_name, target_class)
        
        return False
    
    def _check_generic_type_reference(self, type_name: str, target_class: str) -> bool:
        """检查泛型类型中是否包含目标类引用"""
        # 提取泛型参数部分
        start = type_name.find('<')
        end = type_name.rfind('>')
        
        if start == -1 or end == -1 or start >= end:
            return False
        
        generic_part = type_name[start+1:end]
        
        # 递归解析嵌套泛型
        return self._parse_generic_arguments(generic_part, target_class)
    
    def _parse_generic_arguments(self, generic_args: str, target_class: str) -> bool:
        """解析泛型参数,支持嵌套泛型"""
        if not generic_args:
            return False
        
        # 处理嵌套的尖括号
        args = self._split_generic_arguments(generic_args)
        
        for arg in args:
            arg = arg.strip()
            
            # 直接匹配
            if arg == target_class:
                return True
            
            # 处理通配符类型,如 ? extends UserPO
            if arg.startswith('?'):
                if 'extends' in arg:
                    extends_part = arg.split('extends', 1)[1].strip()
                    if extends_part == target_class:
                        return True
                    # 递归检查嵌套泛型
                    if '<' in extends_part:
                        if self._check_generic_type_reference(extends_part, target_class):
                            return True
                elif 'super' in arg:
                    super_part = arg.split('super', 1)[1].strip()
                    if super_part == target_class:
                        return True
                    # 递归检查嵌套泛型
                    if '<' in super_part:
                        if self._check_generic_type_reference(super_part, target_class):
                            return True
            
            # 递归检查嵌套泛型,如 List<Map<String, UserPO>>
            elif '<' in arg:
                if self._check_generic_type_reference(arg, target_class):
                    return True
        
        return False
    
    def _split_generic_arguments(self, generic_args: str) -> List[str]:
        """分割泛型参数,正确处理嵌套的尖括号"""
        args = []
        current_arg = ""
        bracket_depth = 0
        
        for char in generic_args:
            if char == '<':
                bracket_depth += 1
                current_arg += char
            elif char == '>':
                bracket_depth -= 1
                current_arg += char
            elif char == ',' and bracket_depth == 0:
                # 只有在最外层才分割
                if current_arg.strip():
                    args.append(current_arg.strip())
                current_arg = ""
            else:
                current_arg += char
        
        # 添加最后一个参数
        if current_arg.strip():
            args.append(current_arg.strip())
        
        return args
    
    def _analyze_parameter_usage_context(self, param_type: str, target_class: str) -> str:
        """分析参数中目标类的使用上下文"""
        if param_type == target_class:
            return f"direct usage as {target_class}"
        elif '<' in param_type:
            # 泛型使用
            base_type = param_type.split('<')[0]
            return f"generic usage in {base_type}<...{target_class}...>"
        elif '[]' in param_type:
            # 数组使用
            return f"array usage as {target_class}[]"
        else:
            return f"usage in {param_type}"
    
    def _analyze_return_type_usage_context(self, return_type: str, target_class: str) -> str:
        """分析返回类型中目标类的使用上下文"""
        if return_type == target_class:
            return target_class
        elif '<' in return_type:
            # 泛型使用
            base_type = return_type.split('<')[0]
            return f"{base_type}<...{target_class}...>"
        elif '[]' in return_type:
            # 数组使用
            return f"{target_class}[]"
        else:
            return return_type
    
    def _build_method_signature_context(self, method_name: str, params: List[str], 
                                      return_type: str, method_type: str) -> str:
        """构建方法签名的上下文代码"""
        param_str = ', '.join(params) if params else ""
        
        if method_type == 'constructor':
            return f"{method_name}({param_str})"
        else:
            return_part = f"{return_type} " if return_type else ""
            return f"{return_part}{method_name}({param_str})"
    
    def _find_containing_method(self, ast: javalang.tree.CompilationUnit, variable_node) -> str:
        """查找包含指定变量声明的方法名"""
        # 这是一个简化实现,实际中需要更复杂的AST遍历
        # 查找所有方法声明,看变量是否在其中
        for path, method_node in ast.filter(javalang.tree.MethodDeclaration):
            # 简单检查: 如果变量声明在方法的行号范围内
            if (hasattr(method_node, 'position') and method_node.position and
                hasattr(variable_node, 'position') and variable_node.position):
                # 这里需要更精确的范围检查,暂时返回找到的第一个方法
                return method_node.name
        
        return ""  # 可能是类级别的字段声明
    
    def _build_variable_context(self, variable_node, type_name: str) -> str:
        """构建变量声明的上下文代码"""
        if not variable_node.declarators:
            return f"{type_name} variable;"
        
        declarations = []
        for declarator in variable_node.declarators:
            var_name = declarator.name
            if hasattr(declarator, 'initializer') and declarator.initializer:
                declarations.append(f"{var_name} = ...")
            else:
                declarations.append(var_name)
        
        return f"{type_name} {', '.join(declarations)};"
    
    def _build_field_context(self, field_node, type_name: str) -> str:
        """构建字段声明的上下文代码"""
        if not field_node.declarators:
            return f"{type_name} field;"
        
        declarations = []
        for declarator in field_node.declarators:
            var_name = declarator.name
            if hasattr(declarator, 'initializer') and declarator.initializer:
                declarations.append(f"{var_name} = ...")
            else:
                declarations.append(var_name)
        
        # 添加修饰符信息
        modifiers = []
        if hasattr(field_node, 'modifiers') and field_node.modifiers:
            modifiers = [str(mod) for mod in field_node.modifiers]
        
        modifier_str = ' '.join(modifiers) + ' ' if modifiers else ''
        return f"{modifier_str}{type_name} {', '.join(declarations)};"


class CallChainTracker(ABC):
    """调用链追踪器抽象基类"""
    
    @abstractmethod
    def build_call_graph(self, project_files: List[str]) -> CallGraph:
        """构建项目调用图"""
        pass
    
    @abstractmethod
    def trace_to_root(self, method_name: str, class_name: str) -> List[CallChain]:
        """从指定方法向上追踪到根节点"""
        pass
    
    @abstractmethod
    def find_method_callers(self, method_name: str) -> List[CallerInfo]:
        """查找方法的调用者"""
        pass


class JavaLangCallChainTracker(CallChainTracker):
    """基于javalang的调用链追踪器实现"""
    
    def __init__(self, ast_parser: JavaASTParser):
        self.ast_parser = ast_parser
        self.logger = logging.getLogger(__name__)
        self.call_graph = None
        self.method_registry = {}  # 方法注册表: method_key -> MethodInfo
        self.call_relationships = {}  # 调用关系: method_key -> List[CallerInfo]
    
    def build_call_graph(self, project_files: List[str]) -> CallGraph:
        """构建项目调用图
        
        Args:
            project_files: Java文件路径列表
            
        Returns:
            构建的调用图对象
        """
        self.logger.info(f"开始构建调用图,共 {len(project_files)} 个文件")
        
        # 重置状态
        self.method_registry = {}
        self.call_relationships = {}
        
        # 第一阶段: 扫描所有方法定义
        self._scan_method_definitions(project_files)
        
        # 第二阶段: 分析方法调用关系
        self._analyze_method_calls(project_files)
        
        # 构建调用图对象
        self.call_graph = CallGraph(
            methods=self.method_registry.copy(),
            call_relationships=self.call_relationships.copy()
        )
        
        self.logger.info(f"调用图构建完成,共 {len(self.method_registry)} 个方法,{sum(len(callers) for callers in self.call_relationships.values())} 个调用关系")
        
        return self.call_graph
    
    def _scan_method_definitions(self, project_files: List[str]):
        """扫描所有方法定义,建立方法注册表"""
        self.logger.info("扫描方法定义...")
        
        for file_path in project_files:
            try:
                ast = self.ast_parser.parse_file(file_path)
                if not ast:
                    continue
                
                # 获取类信息
                class_info = self.ast_parser.extract_class_info(ast)
                if not class_info:
                    continue
                
                # 设置文件路径
                class_info.file_path = file_path
                
                # 注册所有方法
                for method in class_info.methods:
                    method.file_path = file_path
                    method_key = self._generate_method_key(method)
                    self.method_registry[method_key] = method
                    
                    # 初始化调用关系列表
                    if method_key not in self.call_relationships:
                        self.call_relationships[method_key] = []
                
            except Exception as e:
                self.logger.error(f"扫描方法定义失败: {file_path}, {e}")
                continue
    
    def _analyze_method_calls(self, project_files: List[str]):
        """分析方法调用关系"""
        self.logger.info("分析方法调用关系...")
        
        for file_path in project_files:
            try:
                ast = self.ast_parser.parse_file(file_path)
                if not ast:
                    continue
                
                # 获取类信息
                class_info = self.ast_parser.extract_class_info(ast)
                if not class_info:
                    continue
                
                # 分析每个方法中的调用
                self._analyze_calls_in_file(ast, class_info, file_path)
                
            except Exception as e:
                self.logger.error(f"分析方法调用失败: {file_path}, {e}")
                continue
    
    def _analyze_calls_in_file(self, ast: javalang.tree.CompilationUnit, class_info: ClassInfo, file_path: str):
        """分析单个文件中的方法调用"""
        # 查找所有方法调用表达式
        for path, node in ast.filter(javalang.tree.MethodInvocation):
            try:
                # 获取调用的方法名
                called_method_name = node.member
                
                # 查找包含此调用的方法
                containing_method = self._find_containing_method_for_call(ast, node)
                if not containing_method:
                    continue
                
                # 生成调用者方法的key
                caller_method_key = self._generate_method_key_from_info(
                    containing_method, class_info.name, class_info.package_name
                )
                
                # 尝试解析被调用的方法
                called_method_candidates = self._resolve_method_call(
                    called_method_name, node, class_info, ast
                )
                
                # 为每个可能的被调用方法建立调用关系
                for called_method_key in called_method_candidates:
                    if called_method_key in self.method_registry:
                        caller_info = CallerInfo(
                            method_info=self.method_registry[caller_method_key],
                            call_line=node.position.line if hasattr(node, 'position') and node.position else 0,
                            call_context=self._extract_call_context(node)
                        )
                        
                        # 避免自调用
                        if called_method_key != caller_method_key:
                            # 添加到被调用方法的调用者列表中
                            if called_method_key not in self.call_relationships:
                                self.call_relationships[called_method_key] = []
                            self.call_relationships[called_method_key].append(caller_info)
                
            except Exception as e:
                self.logger.debug(f"分析方法调用失败: {e}")
                continue
    
    def _find_containing_method_for_call(self, ast: javalang.tree.CompilationUnit, call_node) -> Optional[str]:
        """查找包含指定方法调用的方法名"""
        call_line = call_node.position.line if hasattr(call_node, 'position') and call_node.position else 0
        
        # 查找所有方法声明
        best_method = None
        best_distance = float('inf')
        
        for path, method_node in ast.filter(javalang.tree.MethodDeclaration):
            method_line = method_node.position.line if hasattr(method_node, 'position') and method_node.position else 0
            
            # 如果调用在方法之后,且距离最近
            if method_line <= call_line:
                distance = call_line - method_line
                if distance < best_distance:
                    best_distance = distance
                    best_method = method_node.name
        
        return best_method
    
    def _resolve_method_call(self, method_name: str, call_node, class_info: ClassInfo, ast: javalang.tree.CompilationUnit) -> List[str]:
        """解析方法调用,返回可能的被调用方法key列表"""
        candidates = []
        
        # 1. 检查是否是当前类的方法调用
        for method_key, method_info in self.method_registry.items():
            if (method_info.name == method_name and 
                method_info.class_name == class_info.name and
                method_info.package_name == class_info.package_name):
                candidates.append(method_key)
        
        # 2. 检查是否是通过对象调用的方法
        if hasattr(call_node, 'qualifier') and call_node.qualifier:
            # 尝试解析调用对象的类型
            qualifier_type = self._resolve_qualifier_type(call_node.qualifier, class_info, ast)
            if qualifier_type:
                # 查找该类型的方法
                for method_key, method_info in self.method_registry.items():
                    if (method_info.name == method_name and 
                        self._is_type_match(method_info.class_name, qualifier_type)):
                        candidates.append(method_key)
        
        # 3. 如果没有找到候选方法,尝试全局搜索同名方法
        if not candidates:
            for method_key, method_info in self.method_registry.items():
                if method_info.name == method_name:
                    candidates.append(method_key)
        
        return candidates
    
    def _resolve_qualifier_type(self, qualifier, class_info: ClassInfo, ast: javalang.tree.CompilationUnit) -> Optional[str]:
        """解析方法调用限定符的类型"""
        # 简化实现: 如果是简单的标识符,尝试从变量声明中查找类型
        if hasattr(qualifier, 'member'):
            # 这是一个字段访问,如 obj.method()
            return self._find_variable_type(qualifier.member, ast)
        elif hasattr(qualifier, 'name'):
            # 这是一个简单变量,如 variable.method()
            return self._find_variable_type(qualifier.name, ast)
        
        return None
    
    def _find_variable_type(self, variable_name: str, ast: javalang.tree.CompilationUnit) -> Optional[str]:
        """查找变量的类型"""
        # 查找变量声明
        for path, node in ast.filter(javalang.tree.VariableDeclaration):
            for declarator in node.declarators:
                if declarator.name == variable_name:
                    return self._extract_type_name_from_node(node.type)
        
        # 查找字段声明
        for path, node in ast.filter(javalang.tree.FieldDeclaration):
            for declarator in node.declarators:
                if declarator.name == variable_name:
                    return self._extract_type_name_from_node(node.type)
        
        return None
    
    def _extract_type_name_from_node(self, type_node) -> str:
        """从类型节点提取类型名称"""
        if hasattr(type_node, 'name'):
            return type_node.name
        elif hasattr(type_node, 'type'):
            return self._extract_type_name_from_node(type_node.type)
        else:
            return str(type_node)
    
    def _is_type_match(self, method_class: str, qualifier_type: str) -> bool:
        """检查方法所属类是否与限定符类型匹配"""
        # 简单匹配: 类名相同
        if method_class == qualifier_type:
            return True
        
        # 可以扩展为更复杂的类型匹配逻辑(继承关系等)
        return False
    
    def _extract_call_context(self, call_node) -> str:
        """提取方法调用的上下文信息"""
        method_name = call_node.member
        
        # 构建调用表达式
        if hasattr(call_node, 'qualifier') and call_node.qualifier:
            qualifier_str = self._node_to_string(call_node.qualifier)
            return f"{qualifier_str}.{method_name}(...)"
        else:
            return f"{method_name}(...)"
    
    def _node_to_string(self, node) -> str:
        """将AST节点转换为字符串表示"""
        if hasattr(node, 'name'):
            return node.name
        elif hasattr(node, 'member'):
            return node.member
        else:
            return str(node)
    
    def _generate_method_key(self, method_info: MethodInfo) -> str:
        """生成方法的唯一标识key"""
        param_types = [param.type_name for param in method_info.parameters]
        param_signature = ",".join(param_types)
        return f"{method_info.package_name}.{method_info.class_name}.{method_info.name}({param_signature})"
    
    def _generate_method_key_from_info(self, method_name: str, class_name: str, package_name: str) -> str:
        """从方法信息生成方法key(用于查找)"""
        # 由于不知道具体参数,先尝试无参数版本
        base_key = f"{package_name}.{class_name}.{method_name}"
        
        # 查找匹配的方法
        for key in self.method_registry.keys():
            if key.startswith(base_key + "("):
                return key
        
        # 如果没找到,返回基础key
        return f"{base_key}()"
    
    def trace_to_root(self, method_name: str, class_name: str, package_name: str = "") -> List[CallChain]:
        """从指定方法向上追踪到根节点
        
        实现向上追踪算法,支持循环检测和根节点识别
        
        Args:
            method_name: 方法名
            class_name: 类名
            package_name: 包名
            
        Returns:
            调用链列表,每个调用链从根节点到目标方法
        """
        if not self.call_graph:
            self.logger.error("调用图未构建,请先调用 build_call_graph")
            return []
        
        # 查找目标方法
        target_method_key = self._find_method_key(method_name, class_name, package_name)
        if not target_method_key:
            self.logger.warning(f"未找到方法: {package_name}.{class_name}.{method_name}")
            return []
        
        target_method = self.method_registry[target_method_key]
        self.logger.info(f"开始追踪方法: {target_method_key}")
        
        # 开始追踪
        call_chains = []
        
        # 使用深度优先搜索进行向上追踪
        self._trace_upward_dfs(target_method, call_chains)
        
        # 对调用链进行后处理和优化
        self._post_process_call_chains(call_chains, target_method)
        
        self.logger.info(f"追踪完成,找到 {len(call_chains)} 条调用链")
        
        return call_chains
    
    def _trace_upward_dfs(self, target_method: MethodInfo, call_chains: List[CallChain]):
        """使用深度优先搜索进行向上追踪
        
        实现从目标方法向上追踪到所有可能的根节点,支持循环检测
        """
        target_method_key = self._generate_method_key(target_method)
        
        # 使用栈来实现非递归的深度优先搜索,避免栈溢出
        # 栈元素格式: (current_method, path, visited_in_path, depth)
        stack = [(target_method, [], set(), 0)]
        
        # 全局访问记录,用于避免重复处理相同的路径
        processed_paths = set()
        
        while stack:
            current_method, current_path, path_visited, depth = stack.pop()
            current_method_key = self._generate_method_key(current_method)
            
            # 检查深度限制
            if depth > 50:
                self.logger.warning(f"达到最大追踪深度: {current_method_key}")
                continue
            
            # 检查循环调用
            if current_method_key in path_visited:
                self.logger.debug(f"检测到循环调用: {current_method_key}")
                # 创建循环调用链
                call_chain = CallChain(
                    root_method=current_method,
                    chain_path=current_path.copy(),
                    target_reference=None,
                    depth=depth,
                    is_circular=True
                )
                call_chains.append(call_chain)
                continue
            
            # 生成当前路径的签名,避免重复处理
            path_signature = self._generate_path_signature(current_method, current_path)
            if path_signature in processed_paths:
                continue
            processed_paths.add(path_signature)
            
            # 将当前方法添加到路径访问记录中
            new_path_visited = path_visited.copy()
            new_path_visited.add(current_method_key)
            
            # 查找调用当前方法的方法
            callers = self.call_relationships.get(current_method_key, [])
            
            if not callers:
                # 到达根节点,创建调用链
                call_chain = CallChain(
                    root_method=current_method,
                    chain_path=current_path.copy(),
                    target_reference=None,
                    depth=depth,
                    is_root=self._is_root_method(current_method),
                    is_circular=False
                )
                call_chains.append(call_chain)
                self.logger.debug(f"找到根节点: {current_method_key}, 深度: {depth}")
            else:
                # 继续向上追踪,将所有调用者加入栈中
                for caller_info in callers:
                    caller_method_key = self._generate_method_key(caller_info.method_info)
                    
                    # 创建方法调用对象
                    method_call = MethodCall(
                        caller_method=caller_info.method_info,
                        called_method=current_method,
                        line_number=caller_info.call_line
                    )
                    
                    # 构建新的调用路径(从根到目标的顺序)
                    new_path = [method_call] + current_path
                    
                    # 将调用者加入栈中继续追踪
                    stack.append((
                        caller_info.method_info,
                        new_path,
                        new_path_visited,
                        depth + 1
                    ))
    
    def _generate_path_signature(self, method: MethodInfo, path: List[MethodCall]) -> str:
        """生成路径签名,用于去重"""
        method_key = self._generate_method_key(method)
        if not path:
            return method_key
        
        path_keys = [self._generate_method_key(call.caller_method) for call in path]
        return " -> ".join(path_keys + [method_key])
    
    def _trace_recursive(self, current_method: MethodInfo, current_path: List[MethodCall], 
                        call_chains: List[CallChain], path_visited: set, global_visited: set, depth: int):
        """递归追踪调用链,支持循环检测和根节点识别
        
        保留此方法以保持向后兼容性,但推荐使用 _trace_upward_dfs
        """
        current_method_key = self._generate_method_key(current_method)
        
        # 检查当前路径中的循环调用
        if current_method_key in path_visited:
            self.logger.debug(f"检测到路径循环调用: {current_method_key}")
            # 创建循环调用链
            call_chain = CallChain(
                root_method=current_method,
                chain_path=current_path.copy(),
                target_reference=None,
                depth=depth,
                is_circular=True
            )
            call_chains.append(call_chain)
            return
        
        # 检查深度限制,防止无限递归
        if depth > 50:  # 最大追踪深度
            self.logger.warning(f"达到最大追踪深度: {current_method_key}")
            return
        
        path_visited.add(current_method_key)
        global_visited.add(current_method_key)
        
        # 查找调用当前方法的方法
        callers = self.call_relationships.get(current_method_key, [])
        
        if not callers:
            # 到达根节点,创建调用链
            call_chain = CallChain(
                root_method=current_method,
                chain_path=current_path.copy(),
                target_reference=None,  # 将在后续设置
                depth=depth,
                is_root=self._is_root_method(current_method),
                is_circular=False
            )
            call_chains.append(call_chain)
        else:
            # 继续向上追踪
            for caller_info in callers:
                caller_method_key = self._generate_method_key(caller_info.method_info)
                
                # 跳过已经在全局访问过的方法(避免重复追踪相同的分支)
                if caller_method_key in global_visited and depth > 0:
                    continue
                
                # 创建方法调用对象
                method_call = MethodCall(
                    caller_method=caller_info.method_info,
                    called_method=current_method,
                    line_number=caller_info.call_line
                )
                
                # 添加到路径中
                new_path = [method_call] + current_path
                
                # 递归追踪
                self._trace_recursive(
                    caller_info.method_info, 
                    new_path, 
                    call_chains, 
                    path_visited.copy(), 
                    global_visited, 
                    depth + 1
                )
        
        path_visited.remove(current_method_key)
    
    def _find_method_key(self, method_name: str, class_name: str, package_name: str = "") -> Optional[str]:
        """查找方法的key"""
        # 精确匹配
        for key, method_info in self.method_registry.items():
            if (method_info.name == method_name and 
                method_info.class_name == class_name and
                (not package_name or method_info.package_name == package_name)):
                return key
        
        return None
    
    def find_method_callers(self, method_name: str, class_name: str = "", package_name: str = "") -> List[CallerInfo]:
        """查找方法的调用者
        
        Args:
            method_name: 方法名
            class_name: 类名(可选)
            package_name: 包名(可选)
            
        Returns:
            调用者信息列表
        """
        if not self.call_graph:
            self.logger.error("调用图未构建,请先调用 build_call_graph")
            return []
        
        callers = []
        
        # 查找匹配的方法
        for method_key, method_info in self.method_registry.items():
            if method_info.name == method_name:
                # 检查类名和包名匹配
                if class_name and method_info.class_name != class_name:
                    continue
                if package_name and method_info.package_name != package_name:
                    continue
                
                # 获取该方法的调用者
                method_callers = self.call_relationships.get(method_key, [])
                callers.extend(method_callers)
        
        return callers
    
    def _is_root_method(self, method_info: MethodInfo) -> bool:
        """判断方法是否为根节点方法
        
        根节点方法的特征:
        1. Controller类的public方法(Web入口点)
        2. main方法(应用程序入口点)
        3. 测试方法(@Test注解或测试类中的方法)
        4. 定时任务方法(@Scheduled等注解)
        5. 事件监听器方法(@EventListener等)
        6. 消息队列监听器(@RabbitListener, @KafkaListener等)
        7. 初始化方法(@PostConstruct, @Bean等)
        8. 配置类方法(@Configuration类中的@Bean方法)
        9. 没有被其他方法调用的public方法
        """
        # 1. 检查是否是main方法
        if method_info.name == "main":
            self.logger.debug(f"识别为main方法根节点: {method_info.class_name}.{method_info.name}")
            return True
        
        # 2. 检查是否是Controller类的方法
        if self._is_controller_class(method_info.class_name):
            self.logger.debug(f"识别为Controller根节点: {method_info.class_name}.{method_info.name}")
            return True
        
        # 3. 检查是否是测试方法
        if self._is_test_method(method_info):
            self.logger.debug(f"识别为测试方法根节点: {method_info.class_name}.{method_info.name}")
            return True
        
        # 4. 检查是否是定时任务或事件监听器
        if self._is_scheduled_or_listener_method(method_info):
            self.logger.debug(f"识别为定时任务/监听器根节点: {method_info.class_name}.{method_info.name}")
            return True
        
        # 5. 检查是否是配置类或初始化方法
        if self._is_configuration_method(method_info):
            self.logger.debug(f"识别为配置/初始化根节点: {method_info.class_name}.{method_info.name}")
            return True
        
        # 6. 检查是否是消息队列监听器
        if self._is_message_listener_method(method_info):
            self.logger.debug(f"识别为消息监听器根节点: {method_info.class_name}.{method_info.name}")
            return True
        
        # 7. 检查是否是Service类的入口方法(可能是外部调用的入口点)
        if self._is_service_entry_method(method_info):
            self.logger.debug(f"识别为Service入口根节点: {method_info.class_name}.{method_info.name}")
            return True
        
        # 8. 检查是否是没有调用者的方法(真正的根节点)
        method_key = self._generate_method_key(method_info)
        callers = self.call_relationships.get(method_key, [])
        if not callers and self._is_likely_entry_point(method_info):
            self.logger.debug(f"识别为无调用者根节点: {method_info.class_name}.{method_info.name}")
            return True
        
        return False
    
    def _is_controller_class(self, class_name: str) -> bool:
        """判断是否是Controller类"""
        controller_patterns = [
            'Controller', 'RestController', 'WebController', 
            'ApiController', 'Resource'
        ]
        return any(pattern in class_name for pattern in controller_patterns)
    
    def _is_test_method(self, method_info: MethodInfo) -> bool:
        """判断是否是测试方法"""
        test_patterns = ['test', 'Test']
        return (any(pattern in method_info.name for pattern in test_patterns) or
                any(pattern in method_info.class_name for pattern in test_patterns))
    
    def _is_scheduled_or_listener_method(self, method_info: MethodInfo) -> bool:
        """判断是否是定时任务或事件监听器方法"""
        # 这里可以扩展为检查注解信息
        listener_patterns = ['scheduled', 'listener', 'handler', 'processor']
        return any(pattern in method_info.name.lower() for pattern in listener_patterns)
    
    def _is_service_class(self, class_name: str) -> bool:
        """判断是否是Service类"""
        service_patterns = ['Service', 'ServiceImpl', 'Manager', 'Facade']
        return any(pattern in class_name for pattern in service_patterns)
    
    def _is_configuration_method(self, method_info: MethodInfo) -> bool:
        """判断是否是配置类或初始化方法"""
        # 检查类名是否包含配置相关关键字
        config_class_patterns = ['Config', 'Configuration', 'Setup', 'Init']
        if any(pattern in method_info.class_name for pattern in config_class_patterns):
            return True
        
        # 检查方法名是否包含初始化相关关键字
        init_method_patterns = ['init', 'setup', 'configure', 'bean', 'afterPropertiesSet']
        return any(pattern in method_info.name.lower() for pattern in init_method_patterns)
    
    def _is_message_listener_method(self, method_info: MethodInfo) -> bool:
        """判断是否是消息队列监听器方法"""
        listener_patterns = [
            'rabbit', 'kafka', 'mq', 'jms', 'message', 'queue', 
            'consumer', 'subscriber', 'receive', 'onMessage'
        ]
        
        # 检查类名
        class_name_lower = method_info.class_name.lower()
        if any(pattern in class_name_lower for pattern in listener_patterns):
            return True
        
        # 检查方法名
        method_name_lower = method_info.name.lower()
        return any(pattern in method_name_lower for pattern in listener_patterns)
    
    def _is_service_entry_method(self, method_info: MethodInfo) -> bool:
        """判断是否是Service类的入口方法"""
        if not self._is_service_class(method_info.class_name):
            return False
        
        # Service类中的常见入口方法
        entry_method_patterns = [
            'process', 'handle', 'execute', 'run', 'start', 'invoke',
            'create', 'save', 'update', 'delete', 'query', 'find',
            'get', 'list', 'search', 'export', 'import'
        ]
        
        method_name_lower = method_info.name.lower()
        return any(pattern in method_name_lower for pattern in entry_method_patterns)
    
    def _is_likely_entry_point(self, method_info: MethodInfo) -> bool:
        """判断方法是否可能是入口点"""
        # 排除getter/setter方法
        if (method_info.name.startswith('get') or 
            method_info.name.startswith('set') or
            method_info.name.startswith('is')):
            return False
        
        # 排除toString, equals, hashCode等Object方法
        object_methods = ['toString', 'equals', 'hashCode', 'clone']
        if method_info.name in object_methods:
            return False
        
        # 排除构造函数(虽然构造函数通常不会出现在这里)
        if method_info.name == method_info.class_name:
            return False
        
        # 如果方法名包含业务逻辑相关的关键字,可能是入口点
        business_patterns = [
            'process', 'handle', 'execute', 'run', 'start', 'stop',
            'create', 'build', 'generate', 'calculate', 'compute',
            'validate', 'check', 'verify', 'analyze', 'parse'
        ]
        
        method_name_lower = method_info.name.lower()
        return any(pattern in method_name_lower for pattern in business_patterns)
    
    def _post_process_call_chains(self, call_chains: List[CallChain], target_method: MethodInfo):
        """对调用链进行后处理和优化
        
        处理步骤:
        1. 验证调用链的完整性
        2. 去重相似的调用链
        3. 按重要性和深度排序
        4. 限制输出数量
        5. 标记真正的根节点
        """
        self.logger.info(f"开始后处理 {len(call_chains)} 条调用链")
        
        # 1. 验证和修复调用链
        valid_chains = []
        for chain in call_chains:
            if self._validate_call_chain(chain, target_method):
                # 确保调用链路径的正确性
                self._fix_call_chain_path(chain, target_method)
                valid_chains.append(chain)
            else:
                self.logger.debug(f"移除无效调用链: {self._generate_chain_signature(chain)}")
        
        # 2. 去重相似的调用链
        unique_chains = self._deduplicate_call_chains(valid_chains)
        self.logger.info(f"去重后剩余 {len(unique_chains)} 条调用链")
        
        # 3. 标记真正的根节点
        self._mark_true_root_nodes(unique_chains)
        
        # 4. 按重要性和深度排序
        unique_chains.sort(key=lambda chain: (
            -int(getattr(chain, 'is_root', False)),  # 真正的根节点优先
            -int(getattr(chain, 'is_circular', False)),  # 循环调用排在后面
            chain.depth,  # 深度较小的优先
            -self._calculate_chain_importance(chain)  # 重要性较高的优先
        ))
        
        # 5. 限制调用链数量,避免输出过多
        max_chains = 30
        if len(unique_chains) > max_chains:
            self.logger.info(f"调用链数量过多({len(unique_chains)}),限制为前{max_chains}个")
            unique_chains = unique_chains[:max_chains]
        
        # 6. 更新原列表
        call_chains.clear()
        call_chains.extend(unique_chains)
        
        self.logger.info(f"后处理完成,最终 {len(call_chains)} 条调用链")
    
    def _validate_call_chain(self, call_chain: CallChain, target_method: MethodInfo) -> bool:
        """验证调用链的有效性"""
        # 检查调用链是否为空或循环
        if getattr(call_chain, 'is_circular', False):
            return True  # 循环调用链也是有效的
        
        # 检查根方法是否存在
        if not call_chain.root_method:
            return False
        
        # 检查调用路径的连续性
        if call_chain.chain_path:
            # 验证路径中每个调用的连续性
            for i, call in enumerate(call_chain.chain_path):
                if i == len(call_chain.chain_path) - 1:
                    # 最后一个调用应该指向目标方法
                    if call.called_method.name != target_method.name:
                        self.logger.debug(f"调用链路径不连续: 最后调用 {call.called_method.name} != 目标方法 {target_method.name}")
                        return False
                else:
                    # 中间的调用应该连续
                    next_call = call_chain.chain_path[i + 1]
                    if call.called_method.name != next_call.caller_method.name:
                        self.logger.debug(f"调用链路径不连续: {call.called_method.name} != {next_call.caller_method.name}")
                        return False
        
        return True
    
    def _fix_call_chain_path(self, call_chain: CallChain, target_method: MethodInfo):
        """修复调用链路径,确保从根节点到目标方法的完整路径"""
        if not call_chain.chain_path:
            # 如果没有调用路径,说明根方法就是目标方法
            return
        
        # 确保路径的第一个调用者是根方法
        if call_chain.chain_path:
            first_call = call_chain.chain_path[0]
            if first_call.caller_method.name != call_chain.root_method.name:
                # 路径可能是反向的,需要调整
                self.logger.debug(f"调整调用链路径顺序")
    
    def _mark_true_root_nodes(self, call_chains: List[CallChain]):
        """标记真正的根节点
        
        真正的根节点应该满足:
        1. 没有其他方法调用它
        2. 是已知的入口点类型(Controller, main等)
        """
        for chain in call_chains:
            if getattr(chain, 'is_circular', False):
                continue
            
            root_method_key = self._generate_method_key(chain.root_method)
            callers = self.call_relationships.get(root_method_key, [])
            
            # 如果没有调用者,或者是已知的根节点类型,标记为真正的根节点
            if not callers or self._is_root_method(chain.root_method):
                chain.is_root = True
            else:
                chain.is_root = False
    
    def _deduplicate_call_chains(self, call_chains: List[CallChain]) -> List[CallChain]:
        """去重相似的调用链"""
        unique_chains = []
        seen_signatures = set()
        
        for chain in call_chains:
            # 生成调用链的签名
            signature = self._generate_chain_signature(chain)
            if signature not in seen_signatures:
                seen_signatures.add(signature)
                unique_chains.append(chain)
        
        return unique_chains
    
    def _generate_chain_signature(self, call_chain: CallChain) -> str:
        """生成调用链的唯一签名"""
        if not call_chain.chain_path:
            return f"root:{call_chain.root_method.class_name}.{call_chain.root_method.name}"
        
        path_signature = " -> ".join([
            f"{call.caller_method.class_name}.{call.caller_method.name}"
            for call in call_chain.chain_path
        ])
        return f"{path_signature} -> {call_chain.root_method.class_name}.{call_chain.root_method.name}"
    
    def _calculate_chain_importance(self, call_chain: CallChain) -> int:
        """计算调用链的重要性分数"""
        score = 0
        
        # 根节点加分
        if getattr(call_chain, 'is_root', False):
            score += 100
        
        # Controller类加分
        if self._is_controller_class(call_chain.root_method.class_name):
            score += 50
        
        # Service类加分
        if self._is_service_class(call_chain.root_method.class_name):
            score += 30
        
        # 路径中包含重要类的加分
        for call in call_chain.chain_path:
            if self._is_controller_class(call.caller_method.class_name):
                score += 20
            elif self._is_service_class(call.caller_method.class_name):
                score += 10
        
        return score


class MarkdownReporter(ABC):
    """Markdown报告生成器抽象基类"""
    
    @abstractmethod
    def generate_report(self, analysis_result: AnalysisResult) -> str:
        """生成markdown格式的分析报告"""
        pass
    
    @abstractmethod
    def format_reference_info(self, reference: Reference, target_class: str = None) -> str:
        """格式化引用信息"""
        pass
    
    @abstractmethod
    def format_call_chain(self, call_chain: CallChain) -> str:
        """格式化调用链信息"""
        pass


class JavaLangMarkdownReporter(MarkdownReporter):
    """基于javalang的Markdown报告生成器实现"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
    
    def generate_report(self, analysis_result: AnalysisResult) -> str:
        """生成markdown格式的分析报告
        
        Args:
            analysis_result: 分析结果对象
            
        Returns:
            markdown格式的报告字符串
        """
        if not analysis_result:
            return self._generate_empty_report()
        
        report_lines = []
        
        # 1. 报告标题和概述
        report_lines.append(f"# PO类引用分析报告: {analysis_result.target_class}")
        report_lines.append("")
        report_lines.append("## 概述")
        report_lines.append("")
        
        # 2. 统计信息
        report_lines.extend(self._generate_statistics_section(analysis_result))
        report_lines.append("")
        
        # 3. 引用详情
        if analysis_result.references:
            report_lines.append("## 引用详情")
            report_lines.append("")
            report_lines.extend(self._generate_references_section(analysis_result.references, analysis_result.target_class))
            report_lines.append("")
        
        # 4. 调用链分析
        if analysis_result.call_chains:
            report_lines.append("## 调用链分析")
            report_lines.append("")
            report_lines.extend(self._generate_call_chains_section(analysis_result.call_chains))
            report_lines.append("")
        
        # 5. 汇总统计
        report_lines.extend(self._generate_summary_section(analysis_result))
        
        return "\n".join(report_lines)
    
    def format_reference_info(self, reference: Reference, target_class: str = None) -> str:
        """格式化引用信息
        
        按照要求的格式: package:xx, line:[1,2], methods:xx, 入参:xx, import:xx
        
        Args:
            reference: 引用信息对象
            target_class: 目标PO类名，用于过滤参数
            
        Returns:
            格式化的引用信息字符串
        """
        if not reference:
            return ""
        
        # 构建基础信息
        parts = []
        
        # package信息
        if reference.package_name:
            parts.append(f"package:{reference.package_name}")
        
        # line信息 - 格式化为[1,2,3]的形式
        if reference.line_numbers:
            line_str = "[" + ",".join(map(str, sorted(reference.line_numbers))) + "]"
            parts.append(f"line:{line_str}")
        
        # methods信息
        if reference.method_name:
            parts.append(f"methods:{reference.method_name}")
        
        # 入参信息 - 只显示包含目标类的参数
        if reference.parameters:
            target_params = self._extract_target_class_params(reference.parameters, target_class)
            if target_params:
                params_str = ", ".join(target_params)
                parts.append(f"入参:{params_str}")
        
        # import信息 - 需要包含入参的类的对象
        if reference.reference_type == ReferenceType.IMPORT:
            import_info = self._extract_import_info(reference)
            if import_info:
                parts.append(f"import:{import_info}")
        
        return ", ".join(parts)
    
    def format_call_chain(self, call_chain: CallChain) -> str:
        """格式化调用链信息, 支持层级缩进和树状结构显示
        
        Args:
            call_chain: 调用链对象
            
        Returns:
            格式化的调用链字符串
        """
        if not call_chain:
            return ""
        
        lines = []
        
        # 调用链标题和状态信息
        chain_title = f"### 调用链 (深度: {call_chain.depth})"
        status_markers = []
        
        if getattr(call_chain, 'is_circular', False):
            status_markers.append("🔄 循环调用")
        if getattr(call_chain, 'is_root', False):
            status_markers.append("🌟 根节点")
        
        if status_markers:
            chain_title += f" - {' | '.join(status_markers)}"
        
        lines.append(chain_title)
        lines.append("")
        
        # 调用链概要信息
        lines.append("**调用链概要**:")
        lines.append(f"- 起始点: `{call_chain.root_method.class_name}.{call_chain.root_method.name}()`")
        
        if call_chain.chain_path:
            target_method = call_chain.chain_path[-1].called_method
            lines.append(f"- 目标点: `{target_method.class_name}.{target_method.name}()`")
            lines.append(f"- 调用层级: {len(call_chain.chain_path)} 层")
        else:
            lines.append("- 目标点: 根节点本身")
            lines.append("- 调用层级: 0 层 (直接调用)")
        
        lines.append("")
        
        # 详细调用路径
        lines.append("**详细调用路径**:")
        lines.append("```")
        
        # 构建调用链的树状结构
        chain_lines = self._build_call_chain_tree(call_chain)
        lines.extend(chain_lines)
        
        lines.append("```")
        
        # 添加调用链分析信息
        analysis_info = self._analyze_call_chain(call_chain)
        if analysis_info:
            lines.append("")
            lines.append("**调用链分析**:")
            lines.extend(analysis_info)
        
        return "\n".join(lines)
    
    def _analyze_call_chain(self, call_chain: CallChain) -> List[str]:
        """分析调用链, 提供额外的洞察信息
        
        Args:
            call_chain: 调用链对象
            
        Returns:
            分析信息列表
        """
        analysis = []
        
        # 分析调用链中的类型分布
        if call_chain.chain_path:
            class_types = self._categorize_classes_in_chain(call_chain)
            if class_types:
                analysis.append("- **涉及的组件类型**:")
                for class_type, classes in class_types.items():
                    class_list = ", ".join(classes)
                    analysis.append(f"  - {class_type}: {class_list}")
        
        # 分析调用深度
        if call_chain.depth > 5:
            analysis.append(f"- **深度警告**: 调用链较深 ({call_chain.depth} 层), 可能存在设计问题")
        elif call_chain.depth == 0:
            analysis.append("- **直接调用**: 根节点直接使用目标PO类")
        
        # 分析循环调用
        if getattr(call_chain, 'is_circular', False):
            analysis.append("- **循环调用警告**: 检测到循环调用, 可能导致无限递归")
        
        # 分析根节点类型
        root_analysis = self._analyze_root_method(call_chain.root_method)
        if root_analysis:
            analysis.append(f"- **入口点分析**: {root_analysis}")
        
        return analysis
    
    def _categorize_classes_in_chain(self, call_chain: CallChain) -> Dict[str, List[str]]:
        """对调用链中的类进行分类
        
        Args:
            call_chain: 调用链对象
            
        Returns:
            按类型分类的类名字典
        """
        categories = {
            "Controller": [],
            "Service": [],
            "Repository": [],
            "Util": [],
            "Other": []
        }
        
        # 分析根节点
        root_class = call_chain.root_method.class_name
        root_category = self._classify_class(root_class)
        if root_class not in categories[root_category]:
            categories[root_category].append(root_class)
        
        # 分析调用路径中的类
        for call in call_chain.chain_path:
            caller_class = call.caller_method.class_name
            called_class = call.called_method.class_name
            
            for class_name in [caller_class, called_class]:
                category = self._classify_class(class_name)
                if class_name not in categories[category]:
                    categories[category].append(class_name)
        
        # 移除空的分类
        return {k: v for k, v in categories.items() if v}
    
    def _classify_class(self, class_name: str) -> str:
        """根据类名对类进行分类
        
        Args:
            class_name: 类名
            
        Returns:
            类的分类
        """
        class_name_lower = class_name.lower()
        
        if any(keyword in class_name_lower for keyword in ['controller', 'rest', 'api', 'web']):
            return "Controller"
        elif any(keyword in class_name_lower for keyword in ['service', 'manager', 'facade']):
            return "Service"
        elif any(keyword in class_name_lower for keyword in ['repository', 'dao', 'mapper']):
            return "Repository"
        elif any(keyword in class_name_lower for keyword in ['util', 'helper', 'tool', 'common']):
            return "Util"
        else:
            return "Other"
    
    def _analyze_root_method(self, root_method: MethodInfo) -> str:
        """分析根方法的特征
        
        Args:
            root_method: 根方法信息
            
        Returns:
            分析结果描述
        """
        class_name = root_method.class_name
        method_name = root_method.name
        
        if method_name == "main":
            return "应用程序主入口点"
        elif "controller" in class_name.lower():
            return "Web API入口点"
        elif "service" in class_name.lower():
            return "业务服务入口点"
        elif "test" in class_name.lower() or "test" in method_name.lower():
            return "测试方法入口点"
        elif any(keyword in method_name.lower() for keyword in ['scheduled', 'timer', 'job']):
            return "定时任务入口点"
        elif any(keyword in method_name.lower() for keyword in ['listener', 'handler', 'consumer']):
            return "事件监听器入口点"
        else:
            return "其他类型入口点"
    
    def _generate_empty_report(self) -> str:
        """生成空报告"""
        return """# PO类引用分析报告

## 结果

未找到任何引用.

请检查:
1. PO类名是否正确
2. 项目路径是否正确
3. 是否存在Java源文件
"""
    
    def _generate_statistics_section(self, analysis_result: AnalysisResult) -> List[str]:
        """生成统计信息部分"""
        lines = []
        
        lines.append(f"- **目标PO类**: {analysis_result.target_class}")
        lines.append(f"- **总引用数**: {analysis_result.total_references}")
        lines.append(f"- **涉及文件数**: {analysis_result.affected_files}")
        
        # 按引用类型统计
        if analysis_result.references:
            type_stats = self._calculate_reference_type_statistics(analysis_result.references)
            lines.append("- **引用类型分布**:")
            for ref_type, count in type_stats.items():
                lines.append(f"  - {ref_type}: {count}")
        
        # 调用链统计
        if analysis_result.call_chains:
            lines.append(f"- **调用链数量**: {len(analysis_result.call_chains)}")
            root_chains = sum(1 for chain in analysis_result.call_chains 
                            if getattr(chain, 'is_root', False))
            if root_chains > 0:
                lines.append(f"- **根节点调用链**: {root_chains}")
        
        return lines
    
    def _generate_references_section(self, references: List[Reference], target_class: str = None) -> List[str]:
        """生成引用详情部分"""
        lines = []
        
        # 按文件分组显示引用
        file_groups = self._group_references_by_file(references)
        
        for file_path, file_refs in file_groups.items():
            lines.append(f"### {file_path}")
            lines.append("")
            
            # 按引用类型分组
            type_groups = self._group_references_by_type(file_refs)
            
            for ref_type, type_refs in type_groups.items():
                lines.append(f"**{ref_type.value}引用**:")
                lines.append("")
                
                for ref in type_refs:
                    # 使用要求的格式
                    ref_info = self.format_reference_info(ref, target_class)
                    lines.append(f"- {ref_info}")
                    
                    # 添加上下文代码
                    if ref.context_code:
                        lines.append(f"  ```java")
                        lines.append(f"  {ref.context_code}")
                        lines.append(f"  ```")
                
                lines.append("")
        
        return lines
    
    def _generate_call_chains_section(self, call_chains: List[CallChain]) -> List[str]:
        """生成调用链分析部分, 支持分类和优先级排序"""
        lines = []
        
        # 调用链概述
        lines.append("### 调用链概述")
        lines.append("")
        
        # 统计信息
        total_chains = len(call_chains)
        root_chains = sum(1 for c in call_chains if getattr(c, 'is_root', False))
        circular_chains = sum(1 for c in call_chains if getattr(c, 'is_circular', False))
        
        lines.append(f"- **总调用链数**: {total_chains}")
        lines.append(f"- **根节点调用链**: {root_chains}")
        if circular_chains > 0:
            lines.append(f"- **循环调用链**: {circular_chains}")
        
        # 深度分布
        depth_stats = {}
        for chain in call_chains:
            depth = chain.depth
            depth_stats[depth] = depth_stats.get(depth, 0) + 1
        
        if depth_stats:
            lines.append("- **深度分布**:")
            for depth in sorted(depth_stats.keys()):
                count = depth_stats[depth]
                lines.append(f"  - 深度 {depth}: {count} 条")
        
        lines.append("")
        
        # 按类型分组显示调用链
        grouped_chains = self._group_call_chains_by_type(call_chains)
        
        for group_name, group_chains in grouped_chains.items():
            if not group_chains:
                continue
                
            lines.append(f"### {group_name}")
            lines.append("")
            
            # 按重要性排序组内调用链
            sorted_chains = sorted(group_chains, key=lambda c: (
                -int(getattr(c, 'is_root', False)),
                c.depth,
                c.root_method.class_name,
                c.root_method.name
            ))
            
            for i, chain in enumerate(sorted_chains, 1):
                lines.append(f"#### {group_name} {i}")
                lines.append("")
                
                # 格式化调用链, 但跳过重复的标题
                chain_content = self.format_call_chain(chain)
                chain_lines = chain_content.split('\n')
                # 跳过第一行标题, 从第二行开始
                if len(chain_lines) > 1:
                    lines.extend(chain_lines[1:])
                
                lines.append("")
        
        return lines
    
    def _group_call_chains_by_type(self, call_chains: List[CallChain]) -> Dict[str, List[CallChain]]:
        """按类型对调用链进行分组
        
        Args:
            call_chains: 调用链列表
            
        Returns:
            按类型分组的调用链字典
        """
        groups = {
            "🌟 根节点调用链": [],
            "🔄 循环调用链": [],
            "🎯 直接调用链": [],
            "📊 深层调用链": [],
            "📝 其他调用链": []
        }
        
        for chain in call_chains:
            # 优先级分类: 根节点 > 循环 > 直接 > 深层 > 其他
            if getattr(chain, 'is_root', False):
                groups["🌟 根节点调用链"].append(chain)
            elif getattr(chain, 'is_circular', False):
                groups["🔄 循环调用链"].append(chain)
            elif chain.depth <= 1:
                groups["🎯 直接调用链"].append(chain)
            elif chain.depth >= 5:
                groups["📊 深层调用链"].append(chain)
            else:
                groups["📝 其他调用链"].append(chain)
        
        # 只返回非空的分组
        return {k: v for k, v in groups.items() if v}
    
    def _generate_summary_section(self, analysis_result: AnalysisResult) -> List[str]:
        """生成汇总统计部分, 提供全面的统计信息"""
        lines = []
        
        lines.append("## 汇总统计")
        lines.append("")
        
        # 整体概览
        lines.append("### 📊 整体概览")
        lines.append("")
        lines.append(f"- **分析目标**: `{analysis_result.target_class}`")
        lines.append(f"- **总引用数**: {analysis_result.total_references}")
        lines.append(f"- **涉及文件**: {analysis_result.affected_files} 个")
        lines.append(f"- **调用链数**: {len(analysis_result.call_chains) if analysis_result.call_chains else 0} 条")
        lines.append("")
        
        # 文件级别统计
        if analysis_result.references:
            file_stats = self._calculate_file_statistics(analysis_result.references)
            lines.append("### 📁 文件统计")
            lines.append("")
            lines.append("| 文件路径 | 引用数量 | 引用类型 | 包名 |")
            lines.append("|---------|---------|---------|------|")
            
            # 按引用数量排序
            sorted_files = sorted(file_stats.items(), key=lambda x: x[1]['count'], reverse=True)
            
            for file_path, stats in sorted_files:
                types_str = ", ".join(stats['types'])
                package_name = stats.get('package', 'N/A')
                lines.append(f"| {file_path} | {stats['count']} | {types_str} | {package_name} |")
            
            lines.append("")
        
        # 引用类型分布
        if analysis_result.references:
            type_stats = self._calculate_reference_type_statistics(analysis_result.references)
            lines.append("### 🏷️ 引用类型分布")
            lines.append("")
            
            total_refs = sum(type_stats.values())
            for ref_type, count in sorted(type_stats.items(), key=lambda x: x[1], reverse=True):
                percentage = (count / total_refs * 100) if total_refs > 0 else 0
                lines.append(f"- **{ref_type}**: {count} 次 ({percentage:.1f}%)")
            
            lines.append("")
        
        # 方法级别统计
        if analysis_result.references:
            method_stats = self._calculate_method_statistics(analysis_result.references)
            if method_stats:
                lines.append("### 🔧 方法统计")
                lines.append("")
                lines.append("| 类名 | 方法名 | 引用数量 | 引用类型 |")
                lines.append("|------|--------|---------|---------|")
                
                # 按引用数量排序, 只显示前10个
                sorted_methods = sorted(method_stats.items(), key=lambda x: x[1]['count'], reverse=True)
                
                for method_key, stats in sorted_methods[:10]:
                    if '.' in method_key:
                        class_name, method_name = method_key.split('.', 1)
                    else:
                        class_name, method_name = method_key, 'N/A'
                    
                    types_str = ", ".join(stats['types'])
                    lines.append(f"| {class_name} | {method_name} | {stats['count']} | {types_str} |")
                
                if len(sorted_methods) > 10:
                    lines.append(f"| ... | ... | ... | ... |")
                    lines.append(f"| *共 {len(sorted_methods)} 个方法* | | | |")
                
                lines.append("")
        
        # 调用链统计
        if analysis_result.call_chains:
            lines.append("### 🔗 调用链统计")
            lines.append("")
            
            # 深度分布
            depth_stats = self._calculate_depth_statistics(analysis_result.call_chains)
            lines.append("**深度分布**:")
            for depth in sorted(depth_stats.keys()):
                count = depth_stats[depth]
                lines.append(f"- 深度 {depth}: {count} 条调用链")
            
            lines.append("")
            
            # 根节点统计
            root_stats = self._calculate_root_node_statistics(analysis_result.call_chains)
            if root_stats:
                lines.append("**根节点分布**:")
                for root_type, count in sorted(root_stats.items(), key=lambda x: x[1], reverse=True):
                    lines.append(f"- {root_type}: {count} 个")
                
                lines.append("")
            
            # 特殊情况统计
            special_stats = self._calculate_special_chain_statistics(analysis_result.call_chains)
            if any(special_stats.values()):
                lines.append("**特殊情况**:")
                if special_stats['circular'] > 0:
                    lines.append(f"- 🔄 循环调用: {special_stats['circular']} 条")
                if special_stats['deep'] > 0:
                    lines.append(f"- 📊 深层调用 (>5层): {special_stats['deep']} 条")
                if special_stats['direct'] > 0:
                    lines.append(f"- 🎯 直接调用 (≤1层): {special_stats['direct']} 条")
                
                lines.append("")
        
        # 建议和警告
        recommendations = self._generate_recommendations(analysis_result)
        if recommendations:
            lines.append("### 💡 分析建议")
            lines.append("")
            lines.extend(recommendations)
        
        return lines
    
    def _calculate_special_chain_statistics(self, call_chains: List[CallChain]) -> Dict[str, int]:
        """计算特殊调用链统计"""
        stats = {
            'circular': 0,
            'deep': 0,
            'direct': 0
        }
        
        for chain in call_chains:
            if getattr(chain, 'is_circular', False):
                stats['circular'] += 1
            elif chain.depth > 5:
                stats['deep'] += 1
            elif chain.depth <= 1:
                stats['direct'] += 1
        
        return stats
    
    def _calculate_root_node_statistics(self, call_chains: List[CallChain]) -> Dict[str, int]:
        """计算根节点类型统计"""
        stats = {}
        
        for chain in call_chains:
            root_type = self._classify_class(chain.root_method.class_name)
            stats[root_type] = stats.get(root_type, 0) + 1
        
        return stats
    
    def _generate_recommendations(self, analysis_result: AnalysisResult) -> List[str]:
        """生成分析建议"""
        recommendations = []
        
        # 基于引用数量的建议
        if analysis_result.total_references == 0:
            recommendations.append("- ❌ **未找到引用**: 请检查PO类名是否正确, 或该类可能未被使用")
        elif analysis_result.total_references > 50:
            recommendations.append("- ⚠️ **高耦合警告**: 该PO类被大量引用, 考虑重构以降低耦合度")
        
        # 基于文件分布的建议
        if analysis_result.affected_files > 20:
            recommendations.append("- 📁 **文件分散**: 引用分布在多个文件中, 建议检查是否存在设计问题")
        
        # 基于调用链的建议
        if analysis_result.call_chains:
            deep_chains = [c for c in analysis_result.call_chains if c.depth > 5]
            if deep_chains:
                recommendations.append(f"- 🔗 **深层调用**: 发现 {len(deep_chains)} 条深层调用链, 可能存在过度耦合")
            
            circular_chains = [c for c in analysis_result.call_chains if getattr(c, 'is_circular', False)]
            if circular_chains:
                recommendations.append(f"- 🔄 **循环调用**: 发现 {len(circular_chains)} 条循环调用, 需要检查设计合理性")
        
        # 基于引用类型的建议
        if analysis_result.references:
            import_refs = [r for r in analysis_result.references if r.reference_type == ReferenceType.IMPORT]
            if len(import_refs) != analysis_result.affected_files:
                recommendations.append("- 📦 **导入优化**: 部分文件可能存在未使用的导入, 建议清理")
        
        return recommendations
    
    def _extract_target_class_params(self, parameters: List[str], target_class: str = None) -> List[str]:
        """提取包含目标类的参数信息
        
        Args:
            parameters: 参数列表
            target_class: 目标类名
            
        Returns:
            包含目标类的参数信息列表
        """
        if not parameters:
            return []
        
        target_params = []
        for param in parameters:
            if target_class:
                # 检查参数类型是否包含目标类（支持泛型）
                if (target_class in param or 
                    param.startswith(target_class + " ") or
                    f"<{target_class}>" in param or
                    f"<{target_class}," in param or
                    f",{target_class}>" in param or
                    f",{target_class}," in param):
                    target_params.append(param)
            else:
                # 如果没有指定目标类，返回所有参数
                target_params.append(param)
        
        return target_params
    
    def _extract_import_info(self, reference: Reference) -> str:
        """提取import信息, 包含入参的类的对象
        
        Args:
            reference: 引用信息对象
            
        Returns:
            import信息字符串，包含类的完整路径
        """
        if reference.context_code:
            # 提取import语句中的完整类路径
            import_path = reference.context_code.replace("import ", "").replace(";", "").strip()
            
            # 如果有参数信息，也包含在import信息中
            if reference.parameters:
                param_classes = []
                for param in reference.parameters:
                    # 提取参数中的类名
                    if " " in param:
                        param_type = param.split(" ")[0]
                        param_classes.append(param_type)
                
                if param_classes:
                    return f"{import_path} (参数类型: {', '.join(param_classes)})"
            
            return import_path
        return ""
    
    def _format_method_info(self, method_info: MethodInfo, indent: int = 0) -> str:
        """格式化方法信息"""
        indent_str = "  " * indent
        params = ", ".join([f"{p.type_name} {p.name}" for p in method_info.parameters])
        return f"{indent_str}{method_info.class_name}.{method_info.name}({params})"
    
    def _build_call_chain_tree(self, call_chain: CallChain) -> List[str]:
        """构建调用链的树状结构, 支持层级缩进和美观的树状显示"""
        lines = []
        
        if not call_chain.chain_path:
            # 如果没有调用路径, 说明根方法就是目标方法
            root_display = self._format_method_for_tree(call_chain.root_method, 0, True, True)
            lines.append(root_display)
            return lines
        
        # 从根节点开始构建树状结构
        root_display = self._format_method_for_tree(call_chain.root_method, 0, True, False)
        lines.append(root_display)
        
        # 构建调用链路径
        for i, call in enumerate(call_chain.chain_path):
            is_last = (i == len(call_chain.chain_path) - 1)
            method_display = self._format_method_for_tree(
                call.called_method, 
                i + 1, 
                is_last, 
                is_last,  # 最后一个是目标方法
                call.line_number
            )
            lines.append(method_display)
        
        return lines
    
    def _format_method_for_tree(self, method_info: MethodInfo, level: int, 
                               is_last: bool, is_target: bool, line_number: int = 0) -> str:
        """格式化方法信息用于树状显示
        
        Args:
            method_info: 方法信息
            level: 层级深度
            is_last: 是否是同级的最后一个
            is_target: 是否是目标方法
            line_number: 调用行号
            
        Returns:
            格式化的方法显示字符串
        """
        # 构建缩进和连接符
        if level == 0:
            prefix = "📍 "  # 根节点标记
        else:
            # 构建树状结构的连接线
            indent = "  " * (level - 1)
            if is_last:
                prefix = f"{indent}└─ "
            else:
                prefix = f"{indent}├─ "
        
        # 构建方法签名
        params = ", ".join([f"{p.type_name} {p.name}" for p in method_info.parameters])
        method_signature = f"{method_info.class_name}.{method_info.name}({params})"
        
        # 添加返回类型信息
        if method_info.return_type and method_info.return_type != "void":
            method_signature = f"{method_info.return_type} {method_signature}"
        
        # 添加行号信息
        line_info = ""
        if line_number > 0:
            line_info = f" [line:{line_number}]"
        elif method_info.line_number > 0:
            line_info = f" [line:{method_info.line_number}]"
        
        # 添加特殊标记
        markers = []
        if is_target:
            markers.append("🎯 TARGET")
        if level == 0:
            markers.append("ROOT")
        
        marker_str = ""
        if markers:
            marker_str = f" ({', '.join(markers)})"
        
        return f"{prefix}{method_signature}{line_info}{marker_str}"
    
    def _calculate_reference_type_statistics(self, references: List[Reference]) -> Dict[str, int]:
        """计算引用类型统计"""
        stats = {}
        for ref in references:
            ref_type = ref.reference_type.value
            stats[ref_type] = stats.get(ref_type, 0) + 1
        return stats
    
    def _group_references_by_file(self, references: List[Reference]) -> Dict[str, List[Reference]]:
        """按文件分组引用"""
        groups = {}
        for ref in references:
            file_path = ref.file_path or "未知文件"
            if file_path not in groups:
                groups[file_path] = []
            groups[file_path].append(ref)
        return groups
    
    def _group_references_by_type(self, references: List[Reference]) -> Dict[ReferenceType, List[Reference]]:
        """按引用类型分组"""
        groups = {}
        for ref in references:
            if ref.reference_type not in groups:
                groups[ref.reference_type] = []
            groups[ref.reference_type].append(ref)
        return groups
    
    def _calculate_file_statistics(self, references: List[Reference]) -> Dict[str, Dict[str, Any]]:
        """计算文件级别统计, 包含包名信息"""
        stats = {}
        for ref in references:
            file_path = ref.file_path or "未知文件"
            if file_path not in stats:
                stats[file_path] = {'count': 0, 'types': set(), 'package': ref.package_name or 'N/A'}
            
            stats[file_path]['count'] += 1
            stats[file_path]['types'].add(ref.reference_type.value)
            
            # 更新包名信息（如果当前引用有包名且之前没有）
            if ref.package_name and stats[file_path]['package'] == 'N/A':
                stats[file_path]['package'] = ref.package_name
        
        # 转换set为list
        for file_stats in stats.values():
            file_stats['types'] = list(file_stats['types'])
        
        return stats
    
    def _calculate_method_statistics(self, references: List[Reference]) -> Dict[str, Dict[str, Any]]:
        """计算方法级别统计, 包含引用类型信息"""
        stats = {}
        for ref in references:
            if ref.method_name and ref.class_name:
                method_key = f"{ref.class_name}.{ref.method_name}"
                if method_key not in stats:
                    stats[method_key] = {'count': 0, 'types': set()}
                
                stats[method_key]['count'] += 1
                stats[method_key]['types'].add(ref.reference_type.value)
        
        # 转换set为list
        for method_stats in stats.values():
            method_stats['types'] = list(method_stats['types'])
        
        return stats
    
    def _calculate_depth_statistics(self, call_chains: List[CallChain]) -> Dict[int, int]:
        """计算调用链深度统计"""
        stats = {}
        for chain in call_chains:
            depth = chain.depth
            stats[depth] = stats.get(depth, 0) + 1
        return stats


class CLIInterface:
    """命令行接口"""
    
    def __init__(self):
        self.parser = self._create_parser()
    
    def _create_parser(self) -> argparse.ArgumentParser:
        """创建命令行参数解析器"""
        parser = argparse.ArgumentParser(
            description='PO Reference Finder - Java项目中PO类引用查找工具',
            formatter_class=argparse.RawDescriptionHelpFormatter,
            epilog="""
示例用法:
  python po_reference_finder.py UserPO
  python po_reference_finder.py UserPO --project-path /path/to/project
  python po_reference_finder.py UserPO --output report.md
            """
        )
        
        parser.add_argument(
            'po_class',
            help='要查找的PO类名'
        )
        
        parser.add_argument(
            '--project-path',
            default='.',
            help='Java项目根路径 (默认: 当前目录)'
        )
        
        parser.add_argument(
            '--output',
            help='输出文件路径 (默认: 输出到控制台)'
        )
        
        parser.add_argument(
            '--verbose',
            action='store_true',
            help='显示详细信息'
        )
        
        return parser
    
    def parse_args(self) -> argparse.Namespace:
        """解析命令行参数"""
        return self.parser.parse_args()
    
    def show_help(self):
        """显示帮助信息"""
        self.parser.print_help()


class POReferenceFinder:
    """PO引用查找器主类"""
    
    def __init__(self, ast_parser: JavaASTParser, reference_analyzer: ReferenceAnalyzer,
                 call_chain_tracker: CallChainTracker, markdown_reporter: MarkdownReporter):
        self.ast_parser = ast_parser
        self.reference_analyzer = reference_analyzer
        self.call_chain_tracker = call_chain_tracker
        self.markdown_reporter = markdown_reporter
    
    def find_references(self, po_class: str, project_path: str) -> AnalysisResult:
        """查找PO类的所有引用"""
        # 这里是主要的业务逻辑入口点
        # 具体实现将在后续任务中完成
        pass
    
    def scan_java_files(self, project_path: str) -> List[str]:
        """递归扫描Java文件,排除构建目录
        
        Args:
            project_path: 项目根路径
            
        Returns:
            Java文件路径列表
        """
        java_files = []
        
        # 定义需要排除的目录
        excluded_dirs = {
            'target',      # Maven构建目录
            'build',       # Gradle构建目录
            '.git',        # Git版本控制目录
            '.idea',       # IntelliJ IDEA配置目录
            '.vscode',     # VS Code配置目录
            '.settings',   # Eclipse配置目录
            'node_modules', # Node.js依赖目录
            'out',         # 输出目录
            'bin',         # 编译输出目录
            '.gradle',     # Gradle配置目录
            '.mvn'         # Maven配置目录
        }
        
        try:
            for root, dirs, files in os.walk(project_path):
                # 过滤掉需要排除的目录
                dirs[:] = [d for d in dirs if d not in excluded_dirs]
                
                # 只处理.java文件
                for file in files:
                    if file.endswith('.java'):
                        file_path = os.path.join(root, file)
                        
                        # 验证文件是否可读
                        if self._is_readable_java_file(file_path):
                            java_files.append(file_path)
                        else:
                            logging.warning(f"跳过不可读的Java文件: {file_path}")
            
            logging.info(f"扫描完成,找到 {len(java_files)} 个Java文件")
            return java_files
            
        except Exception as e:
            logging.error(f"扫描Java文件时发生错误: {e}")
            return []
    
    def _is_readable_java_file(self, file_path: str) -> bool:
        """检查Java文件是否可读,并进行基本的编码检测
        
        Args:
            file_path: Java文件路径
            
        Returns:
            文件是否可读
        """
        try:
            # 检查文件是否存在且不为空
            if not os.path.exists(file_path) or os.path.getsize(file_path) == 0:
                return False
            
            # 尝试读取文件内容以验证编码
            encodings = ['utf-8', 'gbk', 'gb2312', 'iso-8859-1', 'cp1252']
            
            for encoding in encodings:
                try:
                    with open(file_path, 'r', encoding=encoding) as f:
                        # 尝试读取前几行来验证编码
                        for _ in range(5):
                            line = f.readline()
                            if not line:
                                break
                    return True
                except UnicodeDecodeError:
                    continue
                except Exception:
                    continue
            
            # 如果所有编码都失败,记录警告但不阻止处理
            logging.warning(f"无法确定文件编码: {file_path}")
            return False
            
        except Exception as e:
            logging.warning(f"检查文件可读性时发生错误: {file_path}, {e}")
            return False


def main():
    """主函数"""
    cli = CLIInterface()
    args = cli.parse_args()
    
    if args.verbose:
        print(f"正在分析PO类: {args.po_class}")
        print(f"项目路径: {args.project_path}")
    
    # 检查项目路径是否存在
    if not os.path.exists(args.project_path):
        print(f"错误: 项目路径不存在: {args.project_path}", file=sys.stderr)
        sys.exit(1)
    
    # 演示AST解析器功能
    print("PO Reference Finder 工具已初始化")
    print("核心接口和数据模型已定义")
    print("Java AST解析器已实现")
    
    # 初始化组件
    ast_parser = JavaLangASTParser()
    reference_analyzer = JavaLangReferenceAnalyzer(ast_parser)
    call_chain_tracker = JavaLangCallChainTracker(ast_parser)
    
    # 使用完整的MarkdownReporter实现
    markdown_reporter = JavaLangMarkdownReporter()
    
    # 创建PO引用查找器
    finder = POReferenceFinder(ast_parser, reference_analyzer, call_chain_tracker, markdown_reporter)
    
    # 使用新的文件扫描功能
    print("开始扫描Java文件...")
    java_files = finder.scan_java_files(args.project_path)
    
    if java_files:
        print(f"\n找到 {len(java_files)} 个Java文件")
        if args.verbose:
            # 演示解析第一个Java文件
            first_file = java_files[0]
            print(f"\n演示解析文件: {first_file}")
            
            ast = ast_parser.parse_file(first_file)
            if ast:
                class_info = ast_parser.extract_class_info(ast)
                if class_info:
                    print(f"类名: {class_info.name}")
                    print(f"包名: {class_info.package_name}")
                    print(f"方法数量: {len(class_info.methods)}")
                    print(f"导入数量: {len(class_info.imports)}")
                    
                    if class_info.methods:
                        print("方法列表:")
                        for method in class_info.methods[:3]:  # 只显示前3个方法
                            params = ", ".join([f"{p.type_name} {p.name}" for p in method.parameters])
                            print(f"  - {method.return_type} {method.name}({params})")
    else:
        print("未找到Java文件")
    
    print("\n等待后续任务实现完整功能...")


if __name__ == "__main__":
    main()