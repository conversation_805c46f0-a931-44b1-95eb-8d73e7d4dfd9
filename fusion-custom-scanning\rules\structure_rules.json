{"method_return_type_rules": [{"description": "方法返回类型检查规则", "enabled": true, "forbidden_return_types": ["Result", "ResultWithTotal", "Result<T>", "ResultWithTotal<T>"], "severity": "warning", "problemtype": "Controller 层变更", "message_template": "方法 '{method_name}' 的返回类型 '{return_type}' 不被允许，建议使用其他返回类型"}], "annotation_issues_rules": [{"description": "@RequestMapping注解内容检查规则", "enabled": true, "annotation_name": "RequestMapping", "required_keywords": ["PluginInfoDef"], "severity": "error", "message_template": "@RequestMapping 注解的 value 必须包含关键字 '{keywords}'", "check_class_annotations": true, "check_method_annotations": false, "problemtype": "Controller 层变更", "suggest": "请在@RequestMapping注解的value中需要包含PluginInfoDef对应的插件名称"}, {"description": "@Api注解内容检查规则", "enabled": true, "annotation_name": "RequestMapping", "required_keywords": ["PluginInfoDef"], "severity": "error", "message_template": "@Api 注解的 value 必须包含关键字 '{keywords}'", "check_class_annotations": true, "check_method_annotations": false, "problemtype": "Controller 层变更", "suggest": "请在@Api注解的value中需要包含PluginInfoDef对应的插件名称"}, {"description": "@OperationLog注解operationType检查规则", "enabled": true, "annotation_name": "OperationLog", "target_attribute": "operationType", "value_range": {"min": 10000, "max": 20000}, "severity": "warning", "message_template": "@OperationLog 注解的 operationType 值 '{value}' 必须在 [{min}, {max}] 范围内", "check_class_annotations": false, "check_method_annotations": true, "constant_matching": true, "problemtype": "权限 ID 调整详细方案", "suggest": "@OperationLog中值异常，融合后的日志编码operationType值使用范围在[10000, 20000]以内"}], "target_detection_rules": {"description": "目标检测规则 - 基于类名和方法名的检测", "enabled": true, "detection_list": [{"class_name": "OilFieldService", "methods": [], "suggest": "请使用DimEnergyService替代OilFieldService", "problemtype": "服务层废弃类详细替换方案"}, {"class_name": "NodeService", "methods": [], "suggest": "请使用EemNodeService替代NodeService", "problemtype": "服务层废弃类详细替换方案"}, {"class_name": "NodeServiceImpl", "methods": [], "suggest": "请使用EemNodeService替代NodeServiceImpl", "problemtype": "服务层废弃类详细替换方案"}, {"class_name": "NodeAuthCheckService", "methods": ["filterNodes", "filterPartNodes"], "suggest": "NodeAuthCheckService部分方法废弃", "problemtype": "服务层废弃类详细替换方案"}, {"class_name": "EemCloudAuthService", "methods": [], "suggest": "EemCloudAuthService废弃", "problemtype": "服务层废弃类详细替换方案"}, {"class_name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "methods": [], "suggest": "AuthUtils废弃", "problemtype": "deprecated_auth"}, {"class_name": "QuantityObjectDao", "methods": [], "suggest": "请使用新的QuantityObjectService替代QuantityObjectDao", "problemtype": "服务层废弃类详细替换方案"}, {"class_name": "QuantityAggregationDataDao", "methods": [], "suggest": "请使用新的QuantityAggregationDataService替代QuantityAggregationDataDao", "problemtype": "服务层废弃类详细替换方案"}, {"class_name": "QuantityManageService", "methods": [], "suggest": "QuantityManageService已经废弃", "problemtype": "服务层废弃类详细替换方案"}, {"class_name": "PipeNetworkConnectionModelDaoImpl", "methods": [], "suggest": "请使用新的管网连接模型数据访问层替代PipeNetworkConnectionModelDaoImpl", "problemtype": "服务层废弃类详细替换方案"}, {"class_name": "EnergySupplyDao", "methods": [], "suggest": "请使用EemEnergySupplyToService替代EnergySupplyDao", "problemtype": "服务层废弃类详细替换方案"}, {"class_name": "EemNoticeService", "methods": [], "suggest": "请使用新的消息推送工具类MessagePushUtils替代EemNoticeService", "problemtype": "服务层废弃类详细替换方案"}, {"class_name": "Topology1Service", "methods": [], "suggest": "Topology1Service已经废弃（平台提供了PipeNetworkConnectionServiceImpl需要重构）", "problemtype": "服务层废弃类详细替换方案"}, {"class_name": "PipeNetworkConnectionModelDao", "methods": [], "suggest": "PipeNetworkConnectionModelDao已经废弃（平台提供了PipeNetworkConnectionServiceImpl需要重构）", "problemtype": "服务层废弃类详细替换方案"}, {"class_name": "PecEventService", "methods": [], "suggest": "PecEventService已经废弃（需要重构）", "problemtype": "服务层废弃类详细替换方案"}, {"class_name": "TopologyCommonService", "methods": [], "suggest": "TopologyCommonService已经废弃", "problemtype": "服务层废弃类详细替换方案"}, {"class_name": "UnitService", "methods": [], "suggest": "UnitService已经废弃，考虑通过EnergyUnitService重构", "problemtype": "服务层废弃类详细替换方案"}, {"class_name": "CommonUtilsService", "methods": [], "suggest": "CommonUtilsService已经废弃，考虑通过eemLogService.writeAddOperationLogs重构", "problemtype": "服务层废弃类详细替换方案"}, {"class_name": "UnitService", "methods": [], "suggest": "UnitService已经废弃，考虑通过EnergyUnitService重构", "problemtype": "服务层废弃类详细替换方案"}, {"class_name": "NodeDao", "methods": [], "suggest": "NodeDao已经废弃，考虑通过EemNodeService重构", "problemtype": "服务层废弃类详细替换方案"}], "severity": "info", "message_template": " '{class_name}' 的方法 '{method_name}' (行: {actual_call_line})已经废弃"}, "regex_pattern_rules": {"description": "正则表达式模式匹配规则", "enabled": true, "patterns": [{"name": "modelServiceUtils_queryWithChildren", "description": "检测modelServiceUtils.queryWithChildren方法调用", "pattern": "modelServiceUtils\\s*\\.\\s*queryWithChildren\\s*\\(.*?\\.class\\s*\\)", "severity": "warning", "suggest": "请使用ParentQueryConditionBuilder.leftJoinSubBuilder替代modelServiceUtils.queryWithChildren泛型", "problemtype": "数据访问层变更详细方案", "message_template": "发现已废弃的方法调用: modelServiceUtils.queryWithChildren泛型 (行: {line})"}]}, "general_settings": {"case_sensitive": true, "include_inherited_methods": false, "include_private_methods": true, "include_protected_methods": true, "include_public_methods": true, "include_package_methods": true}}